<?php
require_once(dirname(__FILE__) . '/expireDataCheckUtil.php');
require_once(dirname(__FILE__) . '/../model/premiumPlanQuery.php');

class SingleFamilyPremiumPlanControl extends SingleFamilyExpireDataCheckUtil
{
    public $commonQuery;
    private $premiumPlanQuery;

    public function __construct($db, $medooDb, $commonQuery)
    {
        parent::__construct($db, $medooDb);
        $this->commonQuery = $commonQuery;
        $this->premiumPlanQuery = new SingleFamilyPremiumPlanQuery($db, $medooDb);
    }

    public function getExpireData($daysBefore)
    {
        // 获取过期数据
        $expireData = $this->premiumPlanQuery->getExpireData($daysBefore);

        // 过滤掉迁移dis的社区
        $expireData = $this->filterMigrateDistributor($expireData);

        // 判断是否开启高级功能开关
        $expireData = $this->filterPremiumPlanSwitch($expireData);

        // 过滤自动扣费的用户
        $expireData = $this->filterPremiumPlanAutoPayUser($expireData);
        LOG_INFO("单住户高级功能过期数据 : " . json_encode($expireData));

        return $expireData;
    }

    // 判断是否开启高级功能开关
    private function filterPremiumPlanSwitch($expireData)
    {
        $filteredData = [];
        foreach ($expireData as $item) {
            if (IsEnablePersonalFeaturePlan($item['Switch'])) {
                $filteredData[] = $item;
            }
        }
        return $filteredData;
    }

    // 过滤高级功能自动扣费用户
    private function filterPremiumPlanAutoPayUser($expireData)
    {
        $filteredData = [];
        foreach ($expireData as $row => $item) {
            if (!$this->commonQuery->IsUserEnableAutoPay($item['PersonalAccountUUID'], AUTO_PAY_ORDER_TYPE_SINGLE)) {
                $filteredData[] = $item;
            }
        }
        return $filteredData;
    }
}