<?php
require_once(dirname(__FILE__) . '/../common/db_common.php');
require_once(dirname(__FILE__) . '/check_expire_common_v4500.php');
const STATIS_FILE = "/var/log/refresh_third_party_token.log";
shell_exec("touch ". STATIS_FILE);

const YALE_AUTH_URL = 'https://oauth.aaecosystem.com';
const YALE_API_URL = 'https://api.aaecosystem.com';
const YALE_CLIENT_ID = 'a4513599-4fac-46b3-8fd2-aab74f56af3c';
const YALE_CLIENT_SECRET = 'c3f6bbbff59e108fc1d880fa5e14de48';
const YALE_API_KEY = '218df464-12c1-4174-8b40-6f5b3006d9be';
const YALE_REDIRECT_URL = 'https://dev-smarthome.akuvox.com/smartplus/DealYaleCode.html';

const QRIO_AUTH_URL = 'https://qrio-production.auth0.com';
const QRIO_API_URL = 'http://sl2-api.qrioinc.com/v1/u';
const QRIO_CLIENT_ID = 'rK0ykDXBMm5Z8RnRn3yNrxN1SwJbj5ie';
const QRIO_CLIENT_SECRET = 'a1qNs4Gx9yKLL9lTP3Qc5-bdn78vdkn3ZdEj-AdeOsQZmR1LRVlO0mFNs2jdN6D9';
const QRIO_REDIRECT_URL = 'https://dev-smarthome.akuvox.com/smartplus/DealQrioCode.html';

const AKCS_MONITOR_ALARM_THIRDPARTY_DEVICE_OPERATION_ALARM = "alarm.crontab.third_party_device.operate_alarm";

//锁品牌类型：0=Qrio,1=Yale,2=BSI,3=Dormakaba,4=SL20,5=Salto,6=Itec,7=TT锁

const QRIO_LOCK_TYPE       = 0;
const YALE_LOCK_TYPE       = 1;
const BSI_LOCK_TYPE_BSI    = 2;
const DORMAKABA_LOCK_TYPE  = 3;
const SL20_LOCK_TYPE       = 4;
const SALTO_LOCK_TYPE      = 5;
const ITEC_LOCK_TYPE       = 6;
const TT_LOCK_TYPE         = 7;

//锁的所在的项目类型
const THIRD_PARTY_LOCK_PROJECT_PERSONAL  = 1;
const THIRD_PARTY_LOCK_PROJECT_RESIDENCE = 2;
const THIRD_PARTY_LOCK_PROJECT_OFFICE    = 3;

//锁的归属等级,1=项目共享,2=楼栋共享,3=家庭独占
const THIRD_PARTY_LOCK_IN_RESIDENCE_PUB    = 1;
const THIRD_PARTY_LOCK_IN_RESIDENCE_BULID  = 2;
const THIRD_PARTY_LOCK_IN_RESIDENCE_APT    = 3;

function httpRequest($method, $url, $headers, $data = '', $is_json = 0)
{
    $curl = curl_init();
    curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($curl, CURLOPT_HEADER, false);
    curl_setopt($curl, CURLOPT_CONNECTTIMEOUT, 10);
    curl_setopt($curl, CURLOPT_URL, $url);
    curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
    if ($method == 'post') {
        curl_setopt($curl, CURLOPT_POST, true);
        if ($is_json) {
            curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
        } else {
            curl_setopt($curl, CURLOPT_POSTFIELDS, http_build_query($data));
        }
    }

    //失败重试3次
    $i = 0;
    do {
        $output = curl_exec($curl);
        $i++;
    } while ($output == false && $i < 4);

    curl_close($curl);

    $refresh_token = $data["refresh_token"];
    TRACE("url:$url refresh_token:$refresh_token Ret:$output");
    return $output;
}

function AddAkcsAlarm($key, $description)
{
    $serverinfo = parse_ini_file("/etc/ip");
    $nsqd_addr = $serverinfo["SERVER_INNER_IP"];

    $data = array();
    $data["node"] = "crontab";
    $data["time"] = date('Y-m-d H:i:s');
    $data["description"] = $description;
    $data["key"] = $key;
    $data["ip"] = $serverinfo["SERVERIP"];
    $data["hostname"] = $serverinfo["AKCS_HOSTNAME"];
    $json_data = json_encode($data);

    $cmd = "curl -s -d '$json_data' " . 'http://' . $nsqd_addr . ':8513/pub?topic=akcs_alarm';
    shell_exec($cmd);
}

function GetPersonalAccountInfo($db, $per_uuid)
{
    #AA.ID = A.ParentID 是因为现在线上小区的parentuuid有问题，后续再改
    $sth = $db->prepare("select AA.Account as dis, A.Account as ins,A.Location as community,P.Name as name,P.UUID as uuid  from PersonalAccount P left join Account A  on P.ParentUUID=A.UUID left join Account AA on AA.ID = A.ParentID  where P.UUID=:uuid");
    $sth->bindParam(':uuid', $per_uuid, PDO::PARAM_STR);
    $sth->execute();
    $accountInfo = $sth->fetch(PDO::FETCH_ASSOC);
    return $accountInfo;
}

function TRACE($content)
{
    $tmpNow = time();
    $Now = date('Y-m-d H:i:s', $tmpNow);
    @file_put_contents(STATIS_FILE, $Now." ".$content, FILE_APPEND);
    @file_put_contents(STATIS_FILE, "\n", FILE_APPEND);
}

/************************* 获取锁的房间、锁名等信息 ***************************/
/*
salto/iTec :SaltoIQ 、ITecLock
dormakaba : DormakabaLock
qrio、yale: ThirdPartyLockDevice
bsi : BSILockDevice
tt锁：目前不收费
*/
function buildLockList(&$item)
{
    switch ($item['LockBrand']) {
        case QRIO_LOCK_TYPE: {
            break;
        }
        case YALE_LOCK_TYPE: {
            break;
        }
        case BSI_LOCK_TYPE_BSI: {
            break;
        }
        case DORMAKABA_LOCK_TYPE: {
            $lockInfo = getDormakabaLockInfo($item);
            $item['LockInfo'] = $lockInfo;
            break;
        }
        case SL20_LOCK_TYPE: {
            break;
        }
        case SALTO_LOCK_TYPE: {
            break;
        }
        case ITEC_LOCK_TYPE: {
            $lockInfo = getItecLockInfo($item);
            $item['LockInfo'] = $lockInfo;
            break;
        }
        case TT_LOCK_TYPE: {
            break;
        }
        default: {
            break;
        }
    }
}

function getItecLockInfo($expireLock)
{       
    global $db;
    $lockInfo = array();
    $sth = $db->prepare("SELECT Name AS LockName,
                                Grade
                                FROM ITecLock
                                WHERE UUID = :uuid;");
    $sth->bindParam(':uuid', $expireLock['LockUUID'], PDO::PARAM_STR);
    $sth->execute();
    $itecLockInfo = $sth->fetch(PDO::FETCH_ASSOC);

    if ($itecLockInfo) {
        $itecLockInfo['Community'] = $expireLock['CommunityName'];
        $itecLockInfo['ProjectType'] = $expireLock['ProjectType'];
        $itecLockInfo['CommunityUnitUUID'] = $expireLock['CommunityUnitUUID'];
        $itecLockInfo['PersonalAccountUUID'] = $expireLock['PersonalAccountUUID'];
        $lockInfo = getLockInfo($itecLockInfo);
    }
    return $lockInfo;
}

function getDormakabaLockInfo($expireLock)
{
    global $db;
    $lockInfo = array();
    $sth = $db->prepare("SELECT Name AS LockName,
                                Grade
                                FROM DormakabaLock 
                                WHERE UUID = :uuid;");
    $sth->bindParam(':uuid', $expireLock['LockUUID'], PDO::PARAM_STR);
    $sth->execute();
    $dormakabaLockInfo = $sth->fetch(PDO::FETCH_ASSOC);

    if ($dormakabaLockInfo) {
        $dormakabaLockInfo['Community'] = $expireLock['CommunityName'];
        $dormakabaLockInfo['ProjectType'] = $expireLock['ProjectType'];
        $dormakabaLockInfo['CommunityUnitUUID'] = $expireLock['CommunityUnitUUID'];
        $dormakabaLockInfo['PersonalAccountUUID'] = $expireLock['PersonalAccountUUID'];
        $lockInfo = getLockInfo($dormakabaLockInfo);
    }
    return $lockInfo; 
}

function getLockInfo($lockInfoData)
{
    global $db;
    $lockInfo = array();
    if ($lockInfoData['ProjectType'] == THIRD_PARTY_LOCK_PROJECT_RESIDENCE) {

        $grade = $lockInfoData['Grade'];
        $lockInfo['CommunityName'] = $lockInfoData['Community']; 
        $lockInfo['LockName'] = $lockInfoData['LockName']; 
        $lockInfo['UnitName'] = '';
        $lockInfo['RoomName'] = '';
        $lockInfo['Grade'] = $lockInfoData['Grade'];
        if ($grade == THIRD_PARTY_LOCK_IN_RESIDENCE_PUB) {
            
        } else {
            $lockInfo['UnitName'] = getCommunityUnitName($db, $lockInfoData['CommunityUnitUUID']);
            if ($grade == THIRD_PARTY_LOCK_IN_RESIDENCE_APT) {
                $lockInfo['RoomName'] = getCommunityRoomName($db, $lockInfoData['PersonalAccountUUID']);
            }
        } 
    } else if ($lockInfoData['ProjectType'] == THIRD_PARTY_LOCK_PROJECT_PERSONAL) {
        $personalAccountInfo = getPersonalAccountInfobyPersonalAccountUUID($lockInfoData['PersonalAccountUUID']); 
        if (!empty($personalAccountInfo)) {
            $lockInfo['LockName'] = $lockInfoData['LockName'];
            $lockInfo['UserEmail'] = $personalAccountInfo['Email'];
            $lockInfo['UserMobile'] = $personalAccountInfo['MobileNumber'];
            $lockInfo['Language'] = $personalAccountInfo['Language'];
            if (!empty($personalAccountInfo['RoomNumber'])) {
                $lockInfo['RoomName'] = $personalAccountInfo['RoomNumber'];
            } else {
                $lockInfo['RoomName'] = $personalAccountInfo['Name'];
            }
        }
    }
    return $lockInfo;
}

function getCommunityUnitName($db, $uuid) {
    $sth = $db->prepare("SELECT UnitName FROM CommunityUnit WHERE UUID = :uuid;");
    $sth->bindParam(':uuid', $uuid, PDO::PARAM_STR);
    $sth->execute();
    $communityUnit = $sth->fetch(PDO::FETCH_ASSOC);
    return $communityUnit ? $communityUnit['UnitName'] : '';
}

function getCommunityRoomName($db, $uuid) {
    $sth = $db->prepare("SELECT RoomNumber from PersonalAccount WHERE UUID = :uuid;");
    $sth->bindParam(':uuid', $uuid, PDO::PARAM_STR);
    $sth->execute();
    $communityRoomName = $sth->fetch(PDO::FETCH_ASSOC);
    return $communityRoomName ? $communityRoomName['RoomNumber'] : '';
}
