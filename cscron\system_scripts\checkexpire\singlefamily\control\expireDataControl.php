<?php
require_once(dirname(__FILE__) . '/residentAppControl.php');
require_once(dirname(__FILE__) . '/premiumPlanControl.php');
require_once(dirname(__FILE__) . '/videoStorageControl.php');
require_once(dirname(__FILE__) . '/thirdPartyLockControl.php');
require_once(dirname(__FILE__) . '/expireDataCheckUtil.php');
require_once(dirname(__FILE__) . '/../model/commonQuery.php');
require_once(dirname(__FILE__) . '/../../data_confusion.php');
require_once(dirname(__FILE__) . '/../../../common/define.php');

class SingleFamilyExpireDataControl
{
    public $commonQuery;
    private $payItemControls = [];
    
    public function __construct($db, $medooDb) {
        $this->commonQuery = new SingleFamilyCommonQuery($db, $medooDb);
        $this->payItemControls[EmailNotifyRule::PAY_ITEM_USER_APP] = new SingleFamilyResidentAppControl($db, $medooDb, $this->commonQuery);
        $this->payItemControls[EmailNotifyRule::PAY_ITEM_FEATURE_PLAN] = new SingleFamilyPremiumPlanControl($db, $medooDb, $this->commonQuery);
        $this->payItemControls[EmailNotifyRule::PAY_ITEM_VIDEO_RECORD] = new SingleFamilyVideoStorageControl($db, $medooDb, $this->commonQuery);
        $this->payItemControls[EmailNotifyRule::PAY_ITEM_THIRDLOCK_PUBLIC_AND_APT] = new SingleFamilyThirdPartyLockControl($db, $medooDb, $this->commonQuery);
    }

    // 获取所有过期数据
    public function getExpireData($daysBefore) {
        $expireData = [];
        foreach ($this->payItemControls as $payItem => $control) {
            $expireData[$payItem] = $control->getExpireData($daysBefore);
        }
        return $expireData;
    }

    // 通过过期的数据列表，获取过期的单住户列表
    public function getPersonalExpireInfoList($expireData)
    {
        $personalMap = [];
        foreach ($expireData as $payItem => $dataList) {
            foreach ($dataList as $data) {
                if (!isset($personalMap[$data['PersonalAccountUUID']])) {
                    $personalMap[$data['PersonalAccountUUID']] = [
                        'DisUUID' => $data['DisUUID'], 
                        'InsUUID' => $data['InsUUID'],
                        'PersonalAccountUUID' => $data['PersonalAccountUUID']
                    ];
                }
            }
        }
        $personalExpireInfoList = array_values($personalMap);
        return $personalExpireInfoList;
    }

    // 获取单住户的收费模式
    public function getPersonalChargeMode($personalExpireInfoList) {
        $personalChargeModeMap = [];
        foreach ($personalExpireInfoList as $data) {
            $personalChargeModeMap[$data['PersonalAccountUUID']] = $this->commonQuery->getPersonalChargeModeByInsUUID($data['InsUUID']);
        }
        return $personalChargeModeMap;
    }

    // key是uuid, value是发送的数据
    public function getEmailDataMap($role, $expireData, $recevierUUIDList)
    {
        // 根据uuid列表，获取邮件数据
        $emailDataMap = [];
        foreach ($expireData as $payItem => $emailData) {
            foreach ($emailData as $data) {
                if ($role == EmailNotifyRole::SEND_TO_DIS) {
                    if (in_array($data['DisUUID'], $recevierUUIDList)) {
                        $emailDataMap[$data['DisUUID']][$payItem][] = $data;
                    }
                } elseif ($role == EmailNotifyRole::SEND_TO_INS) {
                    if (in_array($data['InsUUID'], $recevierUUIDList)) {
                        $emailDataMap[$data['InsUUID']][$payItem][] = $data;
                    }
                } elseif ($role == EmailNotifyRole::SEND_TO_ENDUSER) {
                    if (in_array($data['PersonalAccountUUID'], $recevierUUIDList)) {
                        $emailDataMap[$data['PersonalAccountUUID']][$payItem][] = $data;
                    }
                }
            }
        }

        LOG_INFO("emailDataMap: " . json_encode($emailDataMap));
        return $emailDataMap;
    }

    public function getRecevierUUIDListByExpireData($role, $expireData) {
        if (empty($expireData)) {
            return [];
        }

        $uuidList = [];
        foreach ($expireData as $payItem => $dataList) {
            if (empty($dataList)) {
                continue;
            }

            foreach ($dataList as $data) {
                if ($role == EmailNotifyRole::SEND_TO_DIS) {
                    $uuidList[] = $data['DisUUID'];
                } elseif ($role == EmailNotifyRole::SEND_TO_INS) {
                    $uuidList[] = $data['InsUUID'];
                } elseif ($role == EmailNotifyRole::SEND_TO_ENDUSER) {
                    $uuidList[] = $data['PersonalAccountUUID'];
                }
            }
        }

        LOG_INFO("getRecevierUUIDListByExpireData: " . json_encode($uuidList));
        return array_unique($uuidList);
    }

    // 获取接收者的邮箱,语言,oem
    function getRecevierInfoMap($role, $recevierUUIDList, $emailDataMap)
    {
        $recevierInfoMap = [];
        $recevierDisUUIDMap = $this->getRecevierDisUUIDMap($emailDataMap);

        foreach ($recevierUUIDList as $recevierUUID) {

            $accountInfo = array();
            $billingInfo = array();
            if ($role == EmailNotifyRole::SEND_TO_ENDUSER) {
                $billingInfo = $this->commonQuery->getBillingInfoByPersonalAccountUUID($recevierUUID);
            } else {
                $accountInfo = $this->commonQuery->getAccountInfoByAccountUUID($recevierUUID);
                $billingInfo = $this->commonQuery->getBillingInfoByAccountUUID($recevierUUID);
            }

            $oemName = $this->getDisOemName($recevierDisUUIDMap[$recevierUUID]);


            // 获取接收者的邮箱,语言,oem
            $recevierInfoMap[$recevierUUID] = [
                'oem' => $oemName,
                'email' => $billingInfo['Email'] ?? '',
                'user' => $billingInfo['Account'] ?? '',
                'language' => $billingInfo['Language'] ?? '',
                'loginAccount' => $billingInfo['Account'] ?? '',
            ];

            if ($role == EmailNotifyRole::SEND_TO_INS) {
                $recevierInfoMap[$recevierUUID]['installerID'] = $accountInfo['ID'];
            }
        }
        return $recevierInfoMap;
    }

    // 获取接收者的dis uuid map
    private function getRecevierDisUUIDMap($emailDataMap) {
        $recevierDisUUIDMap = [];
        foreach ($emailDataMap as $recevierUUID => $dataGroups) {
            // $dataGroups 是包含数字键的数组，每个键对应一个数据列表
            foreach ($dataGroups as $groupKey => $dataList) {
                if (!empty($dataList) && isset($dataList[0]['DisUUID'])) {
                    $recevierDisUUIDMap[$recevierUUID] = $dataList[0]['DisUUID'];
                    break;
                }
            }
        }
        return $recevierDisUUIDMap;
    }

    private function getDisOemName($disAccounUUID)
    {
        $oemType = $this->commonQuery->getDisOemType($disAccounUUID);
        return DIS_OEM_NAME[$oemType];
    }
}