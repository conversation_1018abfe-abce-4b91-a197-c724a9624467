<?php

class CommunityVoiceAssitantQuery 
{
    private $db;
    private $medooDb;

    public function __construct($db, $medooDb) {
        $this->db = $db;
        $this->medooDb = $medooDb;
    }

    public function getExpireData($daysBefore)
    {
        $sth = $this->db->prepare("select AAA.UUID as DisUUID, AAA.Account as DisAccount, AA.UUID as InsUUID, AA.Account as InsAccount, 
                        A.UUID as CommunityUUID, A.Location as Community, P.RoomNumber as AptName, R.RoomName as Apt 
                        from PersonalAccountCnf C 
                        join PersonalAccount P on P.Account = C.Account 
                        join CommunityRoom R on R.UUID = P.CommunityRoomUUID 
                        join CommunityUnit U on P.CommunityUnitUUID = U.UUID 
                        join Account A on P.ParentUUID = A.UUID 
                        join Account AA on AA.ManageGroup = A.ManageGroup 
                        join Account AAA on AAA.UUID = AA.ParentUUID 
                    where P.Role = 20 and C.EnableVoiceAssitant = 1 and (C.VoiceAssitantExpireTime > CURDATE() + INTERVAL (:daysBefore) DAY) 
                    and  (C.VoiceAssitantExpireTime <= CURDATE() + INTERVAL (:daysBefore + 1) DAY) and AA.Grade = 22");
        $sth->bindValue(':daysBefore', $daysBefore, PDO::PARAM_INT);
        $sth->execute();

        $expireList = $sth->fetchALL(PDO::FETCH_ASSOC);
        return $expireList;
    }
}