<?php
require_once(dirname(__FILE__) . '/expireDataCheckUtil.php');
require_once(dirname(__FILE__) . '/../model/pmAppQuery.php');

class CommunityPmAppControl extends CommunityExpireDataCheckUtil
{
    public $commonQuery;
    private $pmAppQuery;

    public function __construct($db, $medooDb, $commonQuery)
    {
        parent::__construct($db, $medooDb);
        $this->commonQuery = $commonQuery;
        $this->pmAppQuery = new CommunityPmAppQuery($db, $medooDb);
    }

    public function getExpireData($daysBefore)
    {
        // 获取过期数据
        $expireData = $this->pmAppQuery->getExpireData($daysBefore);

        // 过滤掉迁移dis的社区
        $expireData = $this->filterMigrateDistributor($expireData);

        // 过滤掉自动扣费的用户
        $expireData = $this->filterAutoPayUser($expireData);
        return $expireData;
    }

    private function filterAutoPayUser($expireData)
    {
         $filteredData = [];
        foreach ($expireData as $data) {
            if ($data && !$this->commonQuery->IsCommunityUserEnableAutoPay($data['PersonalAccountUUID'], AUTO_PAY_ENDUSER_ORDER_TYPE_COMMUNITY_APP)
                && !$this->commonQuery->IsCommunityEndUserEnableMixAutoPay($data['PersonalAccountUUID'], AUTO_PAY_ORDER_TYPE_COMMUNITY)) {
                $filteredData[] = $data;
            }
        }
        return $filteredData;
    }
}