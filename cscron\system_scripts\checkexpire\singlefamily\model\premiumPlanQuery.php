<?php
require_once(dirname(__FILE__) . '/../../data_confusion.php');

class SingleFamilyPremiumPlanQuery 
{
    private $db;
    private $medooDb;

    public function __construct($db, $medooDb) {
        $this->db = $db;
        $this->medooDb = $medooDb;
    }

    // 获取过期数据，IsNewBilling = 0的判断PersonalAccount的PhoneExpireTime字段
    public function getExpireData($daysBefore)
    {
        $sth = $this->db->prepare("select AA.UUID as DisUUID, AA.Account as DisAccount, A.Account as InsAccount, A.UUID as InsUUID,
                            P.UUID as PersonalAccountUUID, P.Account as PersonalAccount, P.Name, P.Switch, U.Email, U.MobileNumber, P.PhoneExpireTime, P.EnableSmartHome, P.TimeZone
                            from PersonalAccount P 
                            left join PersonalAccountUserInfo U on U.UUID = P.UserInfoUUID
                            left join Account A on A.UUID = P.ParentUUID
                            left join Account AA on AA.UUID = A.ParentUUID
                            left join PersonalAccountSingleInfo S on S.PersonalAccountUUID = P.UUID
                            where TO_DAYS(P.PhoneExpireTime) = TO_DAYS(NOW()) + :daysBefore 
                            and P.role = :role and S.IsNewBilling = 0 order by A.UUID");

        $sth->bindValue(':daysBefore', $daysBefore, PDO::PARAM_INT);
        $sth->bindValue(':role', ACCOUNT_ROLE_PERSONNAL_MAIN, PDO::PARAM_INT);
        $sth->execute();
        $expireList = $sth->fetchALL(PDO::FETCH_ASSOC);
        
        // 解密字段
        foreach ($expireList as $row => $item) {
            $expireList[$row]['Name'] = DataConfusion::getInstance()->decrypt($item['Name']);
            $expireList[$row]['Email'] = DataConfusion::getInstance()->decrypt($item['Email']);
            $expireList[$row]['MobileNumber'] = DataConfusion::getInstance()->decrypt($item['MobileNumber']);
        }
        return $expireList;
    }
}