#!/bin/bash

set -euo pipefail

RSYNC_PATH=$1              #代码同步到的目录
PROJECT_RUN_PATH=$2        #项目运行路径
IMAGE_ADDR=$3               #镜像拉取地址

cd "$(dirname "$0")"
PKG_ROOT=$(cd ../.. && pwd)

grep_conf() {
    grep -w "$1" "$2" | cut -d'=' -f 2 | awk '{gsub(/^[ \t]+|[ \t]+$/, "");print}'
}

APP_HOME=/usr/local/akcs/cscron

INSTALL_CONF=$RSYNC_PATH/app_backend_install.conf
CSCONFWATCH_EXEC_PATH=/usr/local/akcs/csconfwatch_exec
APP_NAME=cscron
KDC_CONF=/etc/kdc.conf

echo '读取配置'
if [ ! -f $INSTALL_CONF ]; then
    echo "文件不存在：$INSTALL_CONF"
    exit 1
fi

# 检查 KDC_CONF 文件是否存在
if [ ! -f "$KDC_CONF" ]; then
    echo "文件不存在： $KDC_CONF 不存在."
    exit 1
fi

# 读取安装配置
MYSQL_INNER_IP=$(grep_conf 'MYSQL_INNER_IP' $INSTALL_CONF)
NSQLOOKUPD_INNER_IP=$(grep_conf 'NSQLOOKUPD_INNER_IP' $INSTALL_CONF)
ENABLE_DBPROXY=$(grep_conf 'ENABLE_DBPROXY' $INSTALL_CONF || echo '0')
DBPROXY_INNER_IP=$(grep_conf 'DBPROXY_INNER_IP' $INSTALL_CONF)
SYSTEM_AREA=$(grep_conf 'GATEWAY_NUM' $INSTALL_CONF)
GLOBAL_MAC_POOL_IP=$(grep_conf 'GLOBAL_MAC_POOL_IP' $INSTALL_CONF)
WEB_DOMAIN=$(grep_conf 'WEB_DOMAIN' $INSTALL_CONF)
KAFKA_INNER_IP=$(grep_conf 'KAFKA_INNER_IP' $INSTALL_CONF)

#获取数据库密码
ENCRYPTED_DB_PASSWORD=$(grep '^AKCS_DBUSER01=' $KDC_CONF | awk '{print substr($0, index($0, "=") + 1)}')
# 检查 /bin/crypto 是否存在
if [ ! -x "/bin/crypto" ]; then
    echo "加密工具 /bin/crypto 不存在或不可执行."
    exit 1
fi
# 检查 ENCRYPTED_DB_PASSWORD 是否为空
if [ -z "$ENCRYPTED_DB_PASSWORD" ]; then
    echo "错误: 获取到的加密数据库密码为空."
    exit 1
fi

# 解密数据库密码
DECRYPTED_DB_PASSWORD=$(echo "$ENCRYPTED_DB_PASSWORD" | /bin/crypto -d 2>/dev/null)
# 检查解密是否成功
if [ -z "$DECRYPTED_DB_PASSWORD" ]; then
    echo "错误：无法解密数据库密码！"
    exit 1
fi

if [ "$SYSTEM_AREA" -eq 1 ];then
    SERVER_TYPE=eu
elif [ "$SYSTEM_AREA" -eq 3 ];then
    SERVER_TYPE=na
elif [ "$SYSTEM_AREA" -eq 4 ];then
    SERVER_TYPE=in
elif [ "$SYSTEM_AREA" -eq 5 ];then
    SERVER_TYPE=jp
elif [ "$SYSTEM_AREA" -eq 7 ];then
    SERVER_TYPE=au
elif [ "$SYSTEM_AREA" -eq 8 ];then
    SERVER_TYPE=as
elif [ "$SYSTEM_AREA" -eq 9 ];then
    SERVER_TYPE=cn
elif [ "$SYSTEM_AREA" -eq 10 ];then
    SERVER_TYPE=ru
elif [ "$SYSTEM_AREA" -eq 22 ];then
    SERVER_TYPE=az
else 
    SERVER_TYPE=te
fi

# 处理 mysql 相关的脚本
if [ "$ENABLE_DBPROXY" -eq 1 ]; then
    DBPROXY_LINE="\$dbport = 3308;"
    sed -i "s/^.*dbport =.*/${DBPROXY_LINE}/g" "$PKG_ROOT"/cscron/system_scripts/checkexpire/check_expire_common_v4500.php
    sed -i "s/^.*dbport =.*/${DBPROXY_LINE}/g" "$PKG_ROOT"/cscron/system_scripts/clearCapture2.php
    sed -i "s/^.*dbport =.*/${DBPROXY_LINE}/g" "$PKG_ROOT"/cscron/system_scripts/clearModel.php
    sed -i "s/^.*dbport =.*/${DBPROXY_LINE}/g" "$PKG_ROOT"/cscron/system_scripts/common/db_common.php

    DBPROXY_LINE="\$dbhost = \"${DBPROXY_INNER_IP}\";"
    sed -i "s/^.*dbhost =.*/${DBPROXY_LINE}/g" "$PKG_ROOT"/cscron/system_scripts/checkexpire/check_expire_common_v4500.php
    sed -i "s/^.*dbhost =.*/${DBPROXY_LINE}/g" "$PKG_ROOT"/cscron/system_scripts/clearCapture2.php
    sed -i "s/^.*dbhost =.*/${DBPROXY_LINE}/g" "$PKG_ROOT"/cscron/system_scripts/clearModel.php
    #sed -i "s/^.*dbhost =.*/${DBPROXY_LINE}/g" "$PKG_ROOT"/cscron/system_scripts/sql_update.php
    sed -i "s/^.*dbhost =.*/${DBPROXY_LINE}/g" "$PKG_ROOT"/cscron/system_scripts/common/db_common.php
else
    DBPROXY_LINE="\$dbport = 3306;"
    sed -i "s/^.*dbport =.*/${DBPROXY_LINE}/g" "$PKG_ROOT"/cscron/system_scripts/checkexpire/check_expire_common_v4500.php
    sed -i "s/^.*dbport =.*/${DBPROXY_LINE}/g" "$PKG_ROOT"/cscron/system_scripts/clearCapture2.php
    sed -i "s/^.*dbport =.*/${DBPROXY_LINE}/g" "$PKG_ROOT"/cscron/system_scripts/clearModel.php
    sed -i "s/^.*dbport =.*/${DBPROXY_LINE}/g" "$PKG_ROOT"/cscron/system_scripts/common/db_common.php
    
    DBPROXY_LINE="\$dbhost = \"$MYSQL_INNER_IP\";"
    sed -i "s/^.*dbhost =.*/${DBPROXY_LINE}/g" "$PKG_ROOT"/cscron/system_scripts/checkexpire/check_expire_common_v4500.php
    sed -i "s/^.*dbhost =.*/${DBPROXY_LINE}/g" "$PKG_ROOT"/cscron/system_scripts/clearCapture2.php
    sed -i "s/^.*dbhost =.*/${DBPROXY_LINE}/g" "$PKG_ROOT"/cscron/system_scripts/clearModel.php
    #sed -i "s/^.*dbhost =.*/${DBPROXY_LINE}/g" "$PKG_ROOT"/cscron/system_scripts/sql_update.php
    sed -i "s/^.*dbhost =.*/${DBPROXY_LINE}/g" "$PKG_ROOT"/cscron/system_scripts/common/db_common.php
fi


DBPROXY_LINE="\$dbhost = \"$MYSQL_INNER_IP\";"
sed -i "s/^.*dbhost =.*/${DBPROXY_LINE}/g" "$PKG_ROOT"/cscron/system_scripts/landline_notify.php

sed -i "s/lookupdaddr/${NSQLOOKUPD_INNER_IP}/g" "$PKG_ROOT"/cscron/system_scripts/clearCapture2.php

SERVER_LINE="const SERVER_LOCATION = \"$SERVER_TYPE\";"
sed -i "s/^.*const SERVER_LOCATION.*/${SERVER_LINE}/g" "$PKG_ROOT"/cscron/system_scripts/checkexpire/check_expire_common_v4500.php

WEB_DOMAIN_LINE="const WEB_DOMAIN = \"$WEB_DOMAIN\";"
sed -i "s/^.*const WEB_DOMAIN.*/${WEB_DOMAIN_LINE}/g" "$PKG_ROOT"/cscron/system_scripts/checkexpire/check_expire_common_v4500.php


# define相关
sed -i "s/^.*const KAFKA_INNER_IP.*/const KAFKA_INNER_IP=\"${KAFKA_INNER_IP}:8520\";/g
        s/^.*const DB_PASSWORD.*/const DB_PASSWORD=\"${DECRYPTED_DB_PASSWORD}\";/g" "$PKG_ROOT"/cscron/system_scripts/define.php

# 复制处理好的脚本
if [ ! -d /usr/local/akcs/cscron/ ]; then
    mkdir -p /usr/local/akcs/cscron
fi

if [ ! -d /var/log/cscronlog ]; then
    mkdir -p /var/log/cscronlog
fi

cp -rf "$PKG_ROOT"/cscron/ExpireEmailTest/ /usr/local/akcs/cscron
cp -rf "$PKG_ROOT"/cscron/system_scripts/* /usr/local/akcs/cscron
cp -rf "$PKG_ROOT"/cscron/build/set_cron /usr/local/akcs/cscron
cp -f "$PKG_ROOT"/cscron/build/set_scripts.sh /usr/local/akcs/cscron
cp -f "$PKG_ROOT"/cscron/build/clear_cron.sh /usr/local/akcs/cscron
mkdir -p $CSCONFWATCH_EXEC_PATH
cp -f "$PKG_ROOT"/cscron/system_scripts/change_system_conf_by_etcd.php $CSCONFWATCH_EXEC_PATH
chmod 755 -R /usr/local/akcs/cscron

#清除宿主机的定时脚本
bash /usr/local/akcs/cscron/clear_cron.sh

IS_TIME_CHECK=1
#执行时间检查
if ! bash /usr/local/akcs/cscron/set_cron/time_check.sh $IS_TIME_CHECK; then
    echo "时间检查失败，安装失败"
    exit 1
fi

#容器内不执行时间检查
IS_TIME_CHECK=0

#docker变量
log_path="-v /var/log/cscronlog:/var/log/cscronlog"
app_path="-v /usr/local/akcs/cscron:/usr/local/akcs/cscron"
ip_path="-v /etc/ip:/etc/ip"
localtime_path="-v /usr/share/zoneinfo/Asia/Shanghai:/etc/localtime" #统一设置为东八时区
start_cmd="bash /usr/local/akcs/cscron/set_scripts.sh ${SERVER_TYPE} ${IS_TIME_CHECK}"

IMAGE_NAME=${IMAGE_ADDR}/ak_system/app_php8.0:1.1
#docker仓库拉取镜像
docker pull $IMAGE_NAME

if [ `docker ps -a | grep $APP_NAME | wc -l` -gt 0 ];then docker stop $APP_NAME;docker rm $APP_NAME;fi

echo "docker run -d -e TZ=Asia/Shanghai --restart=always $log_path $app_path $ip_path $localtime_path --name $APP_NAME $IMAGE_NAME $start_cmd"
docker run -d -e TZ=Asia/Shanghai --restart=always $log_path $app_path $ip_path $localtime_path --name $APP_NAME $IMAGE_NAME $start_cmd

echo "$APP_NAME install complete."
