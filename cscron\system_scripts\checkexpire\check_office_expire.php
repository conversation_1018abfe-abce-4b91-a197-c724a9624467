<?php

/* 过期逻辑处理 */

require_once(dirname(__FILE__) . '/check_expire_common_v4500.php');
require_once(dirname(__FILE__) . '/notify_check.php');

function PMPayExpire($before)
{
    global $db;

    //获取所有office列表
    $sth = $db->prepare("select AccountUUID,A.ID,O.IsNew from OfficeInfo O left join Account A on A.UUID=O.AccountUUID;");
    $sth->execute();
    $office_list = $sth->fetchALL(PDO::FETCH_ASSOC);
    foreach ($office_list as $row => $office) {

        $is_new_office = NewOfficeCheck::isNew($office); //新办公判断

        if (checkAppIsMigrateDisByUUID($office['AccountUUID'])) {
            continue;
        }
        //检查付费模式
        $ret = getAccountChargeModeByUUID($office['AccountUUID']);
        if (
            $ret && (
                $is_new_office && NewOfficeCheck::needNotifyPm(EmailNotifyRule::PAY_ITEM_USER_APP, $ret, $before) ||
                !$is_new_office && OldOfficeCheck::needNotifyPm(EmailNotifyRule::PAY_ITEM_USER_APP, $ret, $before))
        ) {
            //检查该office下的所有员工的过期时间
            $jsonArr = [];

            $expire_enduser_list = GetOfficeAppExpireList($office, $is_new_office, $before);

            $account_num = count($expire_enduser_list);
            $autopay_usernum = 0;
            foreach ($expire_enduser_list as $key => $value) {
                if (IsUserEnableAutoPay($value['UUID'], AUTO_PAY_ORDER_TYPE_OFFICE)) {
                    $autopay_usernum++;
                    continue;
                }

                if (count($jsonArr) == 20) { //超过20的不发
                    break;
                }

                $userArr = GetOfficeExpireInfoList($is_new_office, $value);
                array_push($jsonArr, $userArr);
            }
            $jsonStr = json_encode($jsonArr);

            if ($account_num - $autopay_usernum <= 0) {
                continue;
            }

            //获取该office的name和pm的信息
            $pm_info = getOfficeAndPMInfoByMngID($office['AccountUUID']);
            foreach ($pm_info as $key => $value) {
                $name = $value['FirstName'] . " " . $value['LastName'];
                $email = getPMAccountEmailByAccountUUID($value['UUID']);

                $email_type = $before == -1 ? "office_account_expire" : "office_account_will_expire";
                $emailInfo['email_type'] = $email_type;
                $emailInfo['project_type'] = PROJECT_TYPE_OFFICE; //走旧办公的链路，用is_new_office标识新办公
                $emailInfo['email'] = $email;
                $emailInfo['language'] = $value["Language"];
                $emailInfo['account_num'] = $account_num;
                $emailInfo['community'] = $ret['Location'];
                $emailInfo['user'] = $name;
                $emailInfo['list'] = $jsonStr;
                $emailInfo['before'] = $before;
                $emailInfo['project_uuid'] = $office['AccountUUID'];
                $emailInfo['is_new_office'] = $is_new_office;
                sendEmailNotify($emailInfo);
                usleep(500 * 1000); //500ms
            }
        }
    }
}

function InstallerPayExpire($before)
{
    global $db;

    //获取所有office列表
    $sth = $db->prepare("select AccountUUID,A.ID,O.IsNew from OfficeInfo O left join Account A on A.UUID=O.AccountUUID;");
    $sth->execute();
    $office_list = $sth->fetchALL(PDO::FETCH_ASSOC);
    foreach ($office_list as $row => $office) {

        $is_new_office = NewOfficeCheck::isNew($office); //新办公判断

        if (checkAppIsMigrateDisByUUID($office['AccountUUID'])) {
            continue;
        }


        //检查付费模式
        $ret = getAccountChargeModeByUUID($office['AccountUUID']);
        if (
            $ret &&
            (
                $is_new_office && NewOfficeCheck::needNotifyIns(EmailNotifyRule::PAY_ITEM_USER_APP, $ret, $before) ||
                (!$is_new_office && OldOfficeCheck::needNotifyIns(EmailNotifyRule::PAY_ITEM_USER_APP, $ret, $before))
            )
        ) {
            //检查该office下的所有员工的过期时间
            $jsonArr = [];

            $expire_enduser_list = GetOfficeAppExpireList($office, $is_new_office, $before);
            $account_num = count($expire_enduser_list);
            $autopay_usernum = 0;
            foreach ($expire_enduser_list as $key => $value) {
                if (IsUserEnableAutoPay($value['UUID'], AUTO_PAY_ORDER_TYPE_OFFICE)) {
                    $autopay_usernum++;
                    continue;
                }

                if (count($jsonArr) == 20) { //超过20的不发
                    break;
                }

                $userArr = GetOfficeExpireInfoList($is_new_office, $value);
                array_push($jsonArr, $userArr);
            }
            $jsonStr = json_encode($jsonArr);

            if ($account_num - $autopay_usernum <= 0) {
                continue;
            }

            //查账单中的installer邮箱,姓名
            $is_show_paylink = SHOW_PAYLINK_INSTALLER;
            $ins_info = getInstallerEmailInfoByInsID($ret['InsID']);

            $email_type = $before == -1 ? "office_account_expire" : "office_account_will_expire";
            $emailInfo['email_type'] = $email_type;
            $emailInfo['project_type'] = PROJECT_TYPE_OFFICE; //走旧办公的链路，用is_new_office标识新办公
            $emailInfo['email'] = $ins_info['Email'];
            $emailInfo['language'] = $ret["InsLanguage"];
            $emailInfo['account_num'] = $account_num;
            $emailInfo['community'] = $ret['Location'];
            $emailInfo['user'] = $ins_info['Account'];
            $emailInfo['list'] = $jsonStr;
            $emailInfo['before'] = $before;
            $emailInfo['is_show_paylink'] = $is_show_paylink;
            $emailInfo['ins_id'] = $ret["InsID"];
            $emailInfo['comm_id'] = $office['ID'];
            $emailInfo["web_ip"] = WEB_DOMAIN;
            $emailInfo['project_uuid'] = $office['AccountUUID'];
            $emailInfo['is_new_office'] = $is_new_office;
            sendEmailNotify($emailInfo);
            usleep(500 * 1000); //500ms
        }
    }
}

function DistributorPayExpire($before)
{
    global $db;

    //获取所有office列表
    $sth = $db->prepare("select AccountUUID,A.ID,O.IsNew from OfficeInfo O left join Account A on A.UUID=O.AccountUUID;");
    $sth->execute();
    $office_list = $sth->fetchALL(PDO::FETCH_ASSOC);
    foreach ($office_list as $row => $office) {

        $is_new_office = NewOfficeCheck::isNew($office); //新办公判断

        if (checkAppIsMigrateDisByUUID($office['AccountUUID'])) {
            continue;
        }
        $is_show_paylink = SHOW_PAYLINK_DISTRIBUTOR;
        //检查付费模式
        $ret = getAccountChargeModeByUUID($office['AccountUUID']);
        if (
            $ret && ($is_new_office && NewOfficeCheck::needNotifyDis(EmailNotifyRule::PAY_ITEM_USER_APP, $ret, $before)) ||
            (!$is_new_office && OldOfficeCheck::needNotifyDis(EmailNotifyRule::PAY_ITEM_USER_APP, $ret, $before))
        ) {

            //检查该office下的所有员工的过期时间
            $jsonArr = [];

            $expire_enduser_list = GetOfficeAppExpireList($office, $is_new_office, $before);

            $account_num = count($expire_enduser_list);
            $autopay_usernum = 0;
            foreach ($expire_enduser_list as $key => $value) {
                if (IsUserEnableAutoPay($value['UUID'], AUTO_PAY_ORDER_TYPE_OFFICE)) {
                    $autopay_usernum++;
                    continue;
                }

                if (count($jsonArr) == 20) { //超过20的不发
                    break;
                }
                $userArr = GetOfficeExpireInfoList($is_new_office, $value);
                array_push($jsonArr, $userArr);
            }
            $jsonStr = json_encode($jsonArr);

            if ($account_num - $autopay_usernum <= 0) {
                continue;
            }

            //查找付费链接中所需的ins信息
            $ins_info = getInstallerEmailInfoByInsID($ret["InsID"]);
            //查账单中的dis邮箱
            $dis_email = getDisEmailByDisAccount($ret['DisAccount']);
            if ($dis_email) {
                $emailInfo['email_type'] = "office_account_will_expire";
                $emailInfo['project_type'] = PROJECT_TYPE_OFFICE; //走旧办公的链路，用is_new_office标识新办公
                $emailInfo['email'] = $dis_email;
                $emailInfo['language'] = $ret["DisLanguage"];
                $emailInfo['account_num'] = $account_num;
                $emailInfo['community'] = $ret['Location'];
                $emailInfo['user'] = $ret['DisAccount'];
                $emailInfo['list'] = $jsonStr;
                $emailInfo['before'] = $before;
                $emailInfo['is_show_paylink'] = $is_show_paylink;
                $emailInfo['ins_uuid'] = $ins_info["UUID"];
                $emailInfo['project_uuid'] = $office['AccountUUID'];
                $emailInfo['comm_id'] = $ret['ID'];
                $emailInfo["web_ip"] = WEB_DOMAIN;
                $emailInfo['project_uuid'] = $office['AccountUUID'];
                $emailInfo['is_new_office'] = $is_new_office;
                sendEmailNotify($emailInfo);
                usleep(500 * 1000); //500ms
            }
            //查找sub_dis信息并给subdis发邮件
            $sub_dis_list = getSubDisAccountInfo($ret["DisUUID"]);
            foreach ($sub_dis_list as $row => $sub_dis) {

                // 判断subdis 是否管理了 当前的ins
                if (!isSubDisMngIns($sub_dis["UUID"], $ins_info["UUID"])) {
                    continue;
                }

                $sub_dis_email = getDisEmailByDisAccount($sub_dis['Account']);
                if (!$sub_dis_email) {
                    continue;
                }

                if (EmailNotifyRule::IfHasPayPermission($sub_dis['SubDisMode'])) {
                    $sub_is_show_paylink = SHOW_PAYLINK_DISTRIBUTOR;
                } else {
                    $sub_is_show_paylink = SHOW_PAYLINK_NONE;
                }

                $emailInfo['email_type'] = "office_account_will_expire";
                $emailInfo['project_type'] = PROJECT_TYPE_OFFICE; //走旧办公的链路，用is_new_office标识新办公
                $emailInfo['email'] = $sub_dis_email;
                $emailInfo['language'] = $sub_dis["Language"];
                $emailInfo['account_num'] = $account_num;
                $emailInfo['community'] = $ret['Location'];
                $emailInfo['user'] = $sub_dis['Account'];
                $emailInfo['list'] = $jsonStr;
                $emailInfo['before'] = $before;
                $emailInfo['is_show_paylink'] = $sub_is_show_paylink;
                $emailInfo['ins_uuid'] = $ins_info["UUID"];
                $emailInfo['project_uuid'] = $office['AccountUUID'];
                $emailInfo['comm_id'] = $ret['ID'];
                $emailInfo["web_ip"] = WEB_DOMAIN;
                $emailInfo['project_uuid'] = $office['AccountUUID'];
                $emailInfo['is_new_office'] = $is_new_office;
                sendEmailNotify($emailInfo);
                usleep(500 * 1000);
            }
        }
    }
}


//参数：before:提前几天的提醒
function checkFeatureInstallerPayExpire($before)
{
    global $db;

    //Installer付费：社区主账号即将过期，提前通知enduser对应的installer
    $sth = $db->prepare("select O.AccountUUID,A.ID,O.IsNew from OfficeInfo O left join Account A on A.UUID=O.AccountUUID where TO_DAYS(O.FeatureExpireTime) = TO_DAYS(NOW()) + :before");
    //查哪几个社区有过期的
    $sth->bindParam(':before', $before, PDO::PARAM_INT);
    $sth->execute();
    $office_list = $sth->fetchALL(PDO::FETCH_ASSOC);


    foreach ($office_list as $row => $office) {
        if (checkAppIsMigrateDisByUUID($office['AccountUUID'])) {
            continue;
        }
        //查对应的installer和distributor的付费模式
        $is_new_office = NewOfficeCheck::isNew($office); //新办公判断
        $ret = getAccountChargeModeByUUID($office['AccountUUID']);
        if ($ret && (
            ($is_new_office && NewOfficeCheck::needNotifyIns(EmailNotifyRule::PAY_ITEM_FEATURE_PLAN, $ret, $before)) ||
            (!$is_new_office && OldOfficeCheck::needNotifyIns(EmailNotifyRule::PAY_ITEM_FEATURE_PLAN, $ret, $before))
        )) {
            $is_show_paylink = SHOW_PAYLINK_INSTALLER;
            //查账单中的installer邮箱,姓名
            $ins_info = getInstallerEmailInfoByInsID($ret['InsID']);
            if ($ins_info) {
                $emailInfo['email_type'] = "office_feature_expire";
                $emailInfo['project_type'] = PROJECT_TYPE_OFFICE;
                $emailInfo['email'] = $ins_info['Email'];
                $emailInfo['language'] = $ins_info["Language"];
                $emailInfo['community'] = $ret['Location'];
                $emailInfo['user'] = $ins_info['Account'];
                $emailInfo['is_show_paylink'] = $is_show_paylink;
                $emailInfo['ins_id'] = $ret["InsID"];
                $emailInfo['comm_id'] = $office['ID'];
                $emailInfo["web_ip"] = WEB_DOMAIN;
                $emailInfo['project_uuid'] = $office['AccountUUID'];
                sendEmailNotify($emailInfo);
                usleep(500 * 1000); //500ms
            }
        }
    }
}

function CheckFeatureExpire()
{
    global $db;

    $sth = $db->prepare("select A.ID,C.AccountUUID from Account A left join OfficeInfo C on A.UUID = C.AccountUUID where (TO_DAYS(FeatureExpireTime) = TO_DAYS(NOW()) - 1)");
    $sth->execute();
    $expire_list = $sth->fetchALL(PDO::FETCH_ASSOC);
    foreach ($expire_list as $row => $office) {
        if (checkAppIsMigrateDisByUUID($office['AccountUUID'])) {
            continue;
        }
        #UpdateDataVersion($db, $community['AccountID']);//在csadapt处理，因为web-task也会有消息到csadapt
        OfficeFeatureExpireNotify($office['ID']);
    }
}

function checkPMPayWillExpire($before)
{
    global $db;

    //获取社区列表
    $sth = $db->prepare("select AccountUUID,IsNew from OfficeInfo where TO_DAYS(FeatureExpireTime) = TO_DAYS(NOW()) + :before");
    $sth->bindParam(':before', $before, PDO::PARAM_INT);
    $sth->execute();
    $office_list = $sth->fetchALL(PDO::FETCH_ASSOC);
    //2.账号即将过期，通知enduser对应的物业PM
    foreach ($office_list as $row => $office) {
        if (checkAppIsMigrateDisByUUID($office['AccountUUID'])) {
            continue;
        }

        $is_new_office = NewOfficeCheck::isNew($office); //新办公判断

        $ret = getAccountChargeModeByUUID($office['AccountUUID']);
        if ($ret && (
            ($is_new_office && NewOfficeCheck::needNotifyPm(EmailNotifyRule::PAY_ITEM_FEATURE_PLAN, $ret, $before)) ||
            (!$is_new_office && OldOfficeCheck::needNotifyPm(EmailNotifyRule::PAY_ITEM_FEATURE_PLAN, $ret, $before))
        )) {
            $send_list = getOfficeAndPMInfoByMngID($office['AccountUUID']);
            foreach ($send_list as $row => $send_info) {
                if ($send_info) {
                    $name = $send_info['FirstName'] . " " . $send_info['LastName'];
                    $email = getPMAccountEmailByAccountUUID($send_info['UUID']);

                    $email_type = $before == -1 ? "office_feature_expire" : "office_pm_feature_will_expire";
                    $emailInfo['email_type'] = $email_type;
                    $emailInfo['project_type'] = PROJECT_TYPE_OFFICE;
                    $emailInfo['email'] = $email;
                    $emailInfo['language'] = $send_info["Language"];
                    $emailInfo['community'] = $ret['Location'];
                    $emailInfo['user'] = $name;
                    $emailInfo['before'] = $before;
                    $emailInfo['project_uuid'] = $office['AccountUUID'];
                    sendEmailNotify($emailInfo);
                    usleep(500 * 1000); //500ms
                }
            }
        }
    }
}

//参数：before:提前几天的提醒
function chekInstallerPayWillExpire($before)
{
    global $db;

    //Installer付费：社区主账号即将过期，提前通知enduser对应的installer
    $sth = $db->prepare("select O.AccountUUID,A.ID,O.IsNew from OfficeInfo O left join Account A on A.UUID=O.AccountUUID where TO_DAYS(O.FeatureExpireTime) = TO_DAYS(NOW()) + :before");
    //查哪几个社区有过期的
    $sth->bindParam(':before', $before, PDO::PARAM_INT);
    $sth->execute();
    $office_list = $sth->fetchALL(PDO::FETCH_ASSOC);
    foreach ($office_list as $row => $office) {
        if (checkAppIsMigrateDisByUUID($office['AccountUUID'])) {
            continue;
        }

        $is_new_office = NewOfficeCheck::isNew($office); //新办公判断

        $ret = getAccountChargeModeByUUID($office['AccountUUID']);
        if (
            $ret &&
            ($is_new_office && NewOfficeCheck::needNotifyIns(EmailNotifyRule::PAY_ITEM_FEATURE_PLAN, $ret, $before)) ||
            (!$is_new_office && OldOfficeCheck::needNotifyIns(EmailNotifyRule::PAY_ITEM_FEATURE_PLAN, $ret, $before))
        ) {
            $is_show_paylink = SHOW_PAYLINK_INSTALLER;
            //查账单中的installer邮箱,姓名
            $ins_info = getInstallerEmailInfoByInsID($ret['InsID']);
            if ($ins_info) {

                $email_type = $before == -1 ? "office_feature_expire" : "office_installer_feature_will_expire";
                $emailInfo['email_type'] = $email_type;
                $emailInfo['project_type'] = PROJECT_TYPE_OFFICE;
                $emailInfo['email'] = $ins_info['Email'];
                $emailInfo['language'] = $ins_info["Language"];
                $emailInfo['community'] = $ret['Location'];
                $emailInfo['user'] = $ins_info['Account'];
                $emailInfo['before'] = $before;
                $emailInfo['is_show_paylink'] = $is_show_paylink;
                $emailInfo['ins_id'] = $ret["InsID"];
                $emailInfo['comm_id'] = $office['ID'];
                $emailInfo["web_ip"] = WEB_DOMAIN;
                $emailInfo['project_uuid'] = $office['AccountUUID'];
                sendEmailNotify($emailInfo);
                usleep(500 * 1000); //500ms
            }
        }
    }
}

//before:提前几天的提醒
function checkDistributorPayWillExpire($before)
{
    global $db;

    //Distributor付费：社区即将过期，提前通知enduser对应的distributor
    $sth = $db->prepare("select AccountUUID,IsNew from OfficeInfo where TO_DAYS(FeatureExpireTime) = TO_DAYS(NOW()) + :before");
    //查哪几个社区有过期的
    $sth->bindParam(':before', $before, PDO::PARAM_INT);
    $sth->execute();
    $office_list = $sth->fetchALL(PDO::FETCH_ASSOC);
    foreach ($office_list as $row => $office) {
        if (checkAppIsMigrateDisByUUID($office['AccountUUID'])) {
            continue;
        }
        $is_new_office = NewOfficeCheck::isNew($office); //新办公判断

        //查对应的distributor的付费模式
        $ret = getAccountChargeModeByUUID($office['AccountUUID']);
        if (
            $ret &&
            ($is_new_office && NewOfficeCheck::needNotifyDis(EmailNotifyRule::PAY_ITEM_FEATURE_PLAN, $ret, $before)) ||
            (!$is_new_office && OldOfficeCheck::needNotifyDis(EmailNotifyRule::PAY_ITEM_FEATURE_PLAN, $ret, $before))
        ) {
            $is_show_paylink = SHOW_PAYLINK_DISTRIBUTOR;
            $dis_email = getDisEmailByDisAccount($ret['DisAccount']);
            //查找付费链接中所需的ins信息
            $ins_info = getInstallerEmailInfoByInsID($ret["InsID"]);
            if ($dis_email) {
                $emailInfo['email_type'] = "office_installer_feature_will_expire";
                $emailInfo['project_type'] = PROJECT_TYPE_OFFICE;
                $emailInfo['email'] = $dis_email;
                $emailInfo['language'] = $ret['DisLanguage'];
                $emailInfo['community'] = $ret['Location'];
                $emailInfo['user'] = $ret['DisAccount'];
                $emailInfo['before'] = $before;
                $emailInfo['is_show_paylink'] = $is_show_paylink;
                $emailInfo['ins_uuid'] = $ins_info["UUID"];
                $emailInfo['project_uuid'] = $office['AccountUUID'];
                $emailInfo['comm_id'] = $ret['ID'];
                $emailInfo["web_ip"] = WEB_DOMAIN;
                sendEmailNotify($emailInfo);
                usleep(500 * 1000); //500ms
            }

            //查找sub_dis信息并给subdis发邮件
            $sub_dis_list = getSubDisAccountInfo($ret["DisUUID"]);
            foreach ($sub_dis_list as $row => $sub_dis) {

                // 判断subdis 是否管理了 当前的ins
                if (!isSubDisMngIns($sub_dis["UUID"], $ins_info["UUID"])) {
                    continue;
                }

                $sub_dis_email = getDisEmailByDisAccount($sub_dis['Account']);
                if (!$sub_dis_email) {
                    continue;
                }

                if (EmailNotifyRule::IfHasPayPermission($sub_dis['SubDisMode'])) {
                    $sub_is_show_paylink = SHOW_PAYLINK_DISTRIBUTOR;
                } else {
                    $sub_is_show_paylink = SHOW_PAYLINK_NONE;
                }

                $emailInfo['email_type'] = "office_installer_feature_will_expire";
                $emailInfo['project_type'] = PROJECT_TYPE_OFFICE;
                $emailInfo['email'] = $sub_dis_email;
                $emailInfo['language'] = $sub_dis['Language'];
                $emailInfo['community'] = $ret['Location'];
                $emailInfo['user'] = $sub_dis['Account'];
                $emailInfo['before'] = $before;
                $emailInfo['is_show_paylink'] = $sub_is_show_paylink;
                $emailInfo['ins_uuid'] = $ins_info["UUID"];
                $emailInfo['project_uuid'] = $office['AccountUUID'];
                $emailInfo['comm_id'] = $ret['ID'];
                $emailInfo["web_ip"] = WEB_DOMAIN;
                sendEmailNotify($emailInfo);
                usleep(500 * 1000);
            }
        }
    }
}

$medooDb = getMedooDb();
$db = $medooDb->pdo;

PMPayExpire(-1);
PMPayExpire(3);
PMPayExpire(5);

InstallerPayExpire(-1);
InstallerPayExpire(3);
InstallerPayExpire(5);
InstallerPayExpire(15);

DistributorPayExpire(5);
DistributorPayExpire(15);

//Feature
CheckFeatureExpire(); //通知后台程序修改配置

checkPMPayWillExpire(-1);
checkPMPayWillExpire(3);
checkPMPayWillExpire(5);

chekInstallerPayWillExpire(-1);
chekInstallerPayWillExpire(3);
chekInstallerPayWillExpire(5);
chekInstallerPayWillExpire(15);

checkDistributorPayWillExpire(5);
checkDistributorPayWillExpire(15);
