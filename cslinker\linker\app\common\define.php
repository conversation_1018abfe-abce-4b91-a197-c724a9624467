<?php

ini_set('date.timezone', 'Asia/Shanghai');

const GATEWAY_NUM = 0;
const GATEWAY_RUCLOUD = 10;

const SMARTHOME_TOKEN = "";
const SMARTHOME_DOMAIN = "api.cloud.akubela.com";

#安装时候拷贝过来的 告警和锁会弹需要用到
const ETC_IP_FILE="/usr/local/akcs/cslinker/linker/app/ip";
const LOG_FILE_PATH="/var/log/cslinkerlog/cslinkerlog.log";
const LOGERR_FILE_PATH="/var/log/cslinkerlog/cslinkerlog_err.log";

#db
const MYSQL_DB_IP="12,3,3.4";
const MYSQL_DB_PORT = 3308;
const MYSQL_DB_PWD="";

#etcd addr
const ETCD_INNER_IP="";

#smg adr
const SMARTHOME_HTTP_GATE="************:8532";

#Yale
const YALE_AUTH_URL = 'https://oauth.aaecosystem.com';
const YALE_API_URL = 'https://api.aaecosystem.com';
const YALE_CLIENT_ID = 'a4513599-4fac-46b3-8fd2-aab74f56af3c';
const YALE_CLIENT_SECRET = 'c3f6bbbff59e108fc1d880fa5e14de48';
const YALE_API_KEY = '218df464-12c1-4174-8b40-6f5b3006d9be';

#QRIO
const QRIO_AUTH_URL = 'https://qrio-production.auth0.com';
const QRIO_API_URL = 'http://sl2-api.qrioinc.com/v1/u';
const QRIO_CLIENT_ID = 'rK0ykDXBMm5Z8RnRn3yNrxN1SwJbj5ie';
const QRIO_CLIENT_SECRET = 'a1qNs4Gx9yKLL9lTP3Qc5-bdn78vdkn3ZdEj-AdeOsQZmR1LRVlO0mFNs2jdN6D9';
const QRIO_REDIRECT_URL = 'https://dev-smarthome.akuvox.com/smartplus/DealQrioCode.html';

#PacPort通用配置
const PAC_TOKEN_VALID_SECONDS = 86000;
const PAC_TOKEN_CHECK_KEY = "pacport_auth_token";
const PACPORT_DEVICE_TYPE = "akuvox";
#pacport测试云环境变量
const PACPORT_API_URL_TEST="https://cloud.sandbox.pacport.dev/api/open/v1";
const PACPORT_AUTH_URL_TEST="https://oauth.sandbox.pacport.dev/oauth2/token";
const PACPORT_AUTH_CLIENT_ID_TEST="3dilc7s85hckjrss9mkb2rchf3";
const PACPORT_AUTH_CLIENT_SECRET_TEST="8emk4lomk9jcnnfhkkt8dgcaq4map1h1mv6djcjd39lrshle4cv";
#pacport生产环境云环境变量
#TODO:待pacport提供prod环境后修改
const PACPORT_API_URL_PROD="https://cloud.sandbox.pacport.dev/api/open/v1";
const PACPORT_AUTH_URL_PROD="https://oauth.sandbox.pacport.dev/oauth2/token";
const PACPORT_AUTH_CLIENT_ID_PROD="3dilc7s85hckjrss9mkb2rchf3";
const PACPORT_AUTH_CLIENT_SECRET_PROD="8emk4lomk9jcnnfhkkt8dgcaq4map1h1mv6djcjd39lrshle4cv";
//pacport生产环境开关 默认关闭
const PACPORT_PROD_ENV_SWITCH=0; 


#redis
const REDISIP = "*************";
const ENABLE_REDIS_SENTINEL = "1";
const REDIS_SENTINEL_HOSTS = "192.168.14:8506, *************:8599";
const REDISSOCKET = 8504;//redis 端口
const REDISPW = "Akcs#xm2610*";//redis 密码
const REDISDB17 = 17;//redis数据库17 自动回弹去重/pacport快递云token存储
const REDISVALIDITYTIME = 30;
const REDIS_WEATHER = 21;

#CAPTURE_TYPE
const THIRD_PARTY_LOCK_CAPTURE_TYPE_CALL = 0;
const THIRD_PARTY_LOCK_CAPTURE_TYPE_TMPKEY = 1;
const THIRD_PARTY_LOCK_CAPTURE_TYPE_LOCALKEY = 2;
const THIRD_PARTY_LOCK_CAPTURE_TYPE_RFCARD = 3;
const THIRD_PARTY_LOCK_CAPTURE_TYPE_FACE = 4;
const THIRD_PARTY_LOCK_CAPTURE_TYPE_NFC = 100;
const THIRD_PARTY_LOCK_CAPTURE_TYPE_BLE = 101;
const THIRD_PARTY_LOCK_CAPTURE_TYPE_AUTO_LOCK = 200;
const THIRD_PARTY_LOCK_CAPTURE_TYPE_LOCK_BY_APP = 201;
// ThirdPartyLockCapture表的FailedType字段
const THIRD_LOCK_FAILED_TYPE_SUCCESS = 0;
const THIRD_LOCK_FAILED_TYPE_FAILED = 1;
const THIRD_LOCK_FAILED_TYPE_OUTSIDE_AUTHORIZED_TIME = 5;    
const THIRD_LOCK_FAILED_TYPE_NO_CONNECT_GATEWAY = 7;  
const THIRD_LOCK_FAILED_TYPE_GATEWAY_BUSY = 8; 
const THIRD_LOCK_FAILED_TYPE_TTLOCK_NOT_EXIST = 13061112;  //TT锁被第三方删除后 app要弹窗提示
const THIRD_LOCK_FAILED_TYPE_ITECLOCK_NOT_EXIST = 13051137;  //ITec锁被第三方删除
const THIRD_LOCK_FAILED_TYPE_DORMAKABA_NOT_EXIST = 13001028;  //Dormakaba锁被第三方删除
#LOCK_STATUS
const THIRD_PARTY_LOCK_STATUS_CLOSE = 0;
const THIRD_PARTY_LOCK_STATUS_OPEN = 1;

#KAFKA_CONFIG
const KAFKA_INNER_IP = "*************:8520";

const KAFKA_CONSUMER_NUM = 2;
const BUSINESSWORK_NUM=8;
const KAFKA_CSLINKER_TOPIC = "ak_cslinker";
const KAFKA_CSLINKER_CONSUMER = "ak_cslinker_group";

#linker->route 消息类型区分
const PROJECT_TYPE_RESIDENCE = 0;
const PROJECT_TYPE_OFFICE = 1;
const PROJECT_TYPE_PERSONAL = 2;
const PROJECT_TYPE_NEWOFFICE = 3;
#WEB
const WEB_BACKEND_DOMAIN = "dev.akuvox.com";

#KIT_CONFIG
const KIT_CLIENT_ID = "akcs-csmain-client";
const KIT_CLIENT_SECRET = "8DkVZSK0N81fsZkle1lhc1eHibsCWJpUGRGAec9F8rSYIsW0yc8tSO080odJiHst";
const KIT_CLIENT_ID_SECRET = KIT_CLIENT_ID ."_". KIT_CLIENT_SECRET;
const KIT_API_URL = "https://" . WEB_BACKEND_DOMAIN . "/web-server/v3/basic/single";
const KIT_API_COMM_URL = "https://" . WEB_BACKEND_DOMAIN . "/web-server/v3/basic/community";
const WEB_ADAPT_ENTRY = "";

#三方锁数据库type
const THIRD_PARTY_LOCK_TYPE_QRIO = 0;
const THIRD_PARTY_LOCK_TYPE_YALE = 1;
const THIRD_PARTY_LOCK_TYPE_BSI = 2;
const THIRD_PARTY_LOCK_TYPE_DORMAKABA = 3;
const THIRD_PARTY_LOCK_TYPE_SL20 = 4;
const THIRD_PARTY_LOCK_TYPE_SALTO = 5;
const THIRD_PARTY_LOCK_TYPE_ITEC = 6;
const THIRD_PARTY_LOCK_TYPE_TT = 7;
