<?php
require_once(dirname(__FILE__) . '/expireDataCheckUtil.php');
require_once(dirname(__FILE__) . '/../model/videoStorageQuery.php');
require_once(dirname(__FILE__) . '/../../data_confusion.php');

// 视频存储过期检测
class SingleFamilyVideoStorageControl extends SingleFamilyExpireDataCheckUtil
{
    public $commonQuery;
    private $videoStorageQuery;

    const AUTO_PAY_ORDER_TYPE_SINGLE_VIDEO_STORAGE = 5;

    public function __construct($db, $medooDb, $commonQuery)
    {
        parent::__construct($db, $medooDb);
        $this->commonQuery = $commonQuery;
        $this->videoStorageQuery = new SingleFamilyVideoStorageQuery($db, $medooDb);
    }

    public function getExpireData($daysBefore)
    {
        // 获取过期数据
        $expireData = $this->videoStorageQuery->getExpireData($daysBefore);

        // 过滤掉迁移dis
        $expireData = $this->filterMigrateDistributor($expireData);

        // 过滤掉自动扣费的用户
        $expireData = $this->filterVideoStorageAutoPayUser($expireData);   

        LOG_INFO("单住户视频存储过期数据: " . json_encode($expireData));
        return $expireData;
    }

    private function filterVideoStorageAutoPayUser($expireData)
    {
        $filteredData = [];
        foreach ($expireData as $data) {
            if (!$this->videoStorageQuery->IsUserEnableVideoStorageAutoPay($data['PersonalAccountUUID'], self::AUTO_PAY_ORDER_TYPE_SINGLE_VIDEO_STORAGE)) {
                $filteredData[] = $data;
            }
        }
        return $filteredData;
    } 
}