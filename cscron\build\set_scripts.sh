#!/bin/bash

set -euo pipefail

SERVER_TYPE=$1
IS_TIME_CHECK=$2

APP_HOME=/usr/local/akcs/cscron

#crontab 文件路径
CRON_FILE_PATH="/var/spool/cron/crontabs/root"
CRON_DIR=$(dirname "$CRON_FILE_PATH")

if [ ! -d "$CRON_DIR" ]; then
    mkdir -p "$CRON_DIR"
fi

#获取对应服务器的构建脚本
SET_CRON_SCRIPT_NAME="${SERVER_TYPE}.sh"

# 添加到 crontab
echo "执行cron设置脚本: $SET_CRON_SCRIPT_NAME"
bash $APP_HOME/set_cron/$SET_CRON_SCRIPT_NAME $IS_TIME_CHECK


sleep 1

if [ ! -d /var/log/cspushlog ]; then
    mkdir -p /var/log/cspushlog
fi

chmod 0600 /var/spool/cron/crontabs/root
service cron restart

# 容器常驻
tail -f /dev/null

#去掉 20231129
#重新开启 但是不能放这里 后面每开一套环境都需要添加白名单，并且一些内网还用不了。
#这样就导致，我们的告警不在白名单还需要区分内网环境不告警，处理不了。
#直接弄到数据库主节点处理
# echo '拉取 mac 信息'
# GLOBAL_OUTER_IP_LINE="const GLOBAL_MACPOOL_IP = \"${GLOBAL_MAC_POOL_IP}\";"
# DB_LINE="\$dbhost = \"$MYSQL_INNER_IP\";"

# sed -i "s/^.*const GLOBAL_MACPOOL_IP.*/${GLOBAL_OUTER_IP_LINE}/g" /usr/local/akcs/cscron/get_acks_macpool.php
# sed -i "s/^.*const GLOBAL_MACPOOL_IP.*/${GLOBAL_OUTER_IP_LINE}/g" /usr/local/akcs/cscron/remove_acks_macpool.php
# sed -i "s/^.*dbhost=.*/${DB_LINE}/g" /usr/local/akcs/cscron/get_acks_macpool.php
# sed -i "s/^.*dbhost=.*/${DB_LINE}/g" /usr/local/akcs/cscron/remove_acks_macpool.php
# if ! grep -q '/usr/local/akcs/cscron/get_acks_macpool.php' /var/spool/cron/crontabs/root; then
#    echo '15 9 * * * /usr/local/bin/php /usr/local/akcs/cscron/get_acks_macpool.php >/dev/null 2>&1' >> /var/spool/cron/crontabs/root
#    service cron reload
# fi

# if ! grep -q '/usr/local/akcs/cscron/remove_acks_macpool.php' /var/spool/cron/crontabs/root; then
#    echo '0 */1 * * *  /usr/local/bin/php /usr/local/akcs/cscron/remove_acks_macpool.php >/dev/null 2>&1' >> /var/spool/cron/crontabs/root
#    service cron reload
# fi







