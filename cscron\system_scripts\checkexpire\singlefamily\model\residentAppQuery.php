<?php
require_once(dirname(__FILE__) . '/../../data_confusion.php');

class SingleFamilyResidentAppQuery 
{
    private $db;
    private $medooDb;

    public function __construct($db, $medooDb) {
        $this->db = $db;
        $this->medooDb = $medooDb;
    }

    // 获取过期数据，IsNewBilling的判断PersonalAccount的ExpireTime字段
    public function getExpireData($daysBefore) {
        $sth = $this->db->prepare("select AA.UUID as DisUUID, AA.Account as DisAccount, A.UUID as InsUUID, A.Account as InsAccount,
                                    P.UUID as PersonalAccountUUID, P.Name, U.MobileNumber, U.Email, P.ExpireTime, P.EnableSmartHome, P.TimeZone
                                from PersonalAccount P
                                left join PersonalAccountUserInfo U on P.UserInfoUUID = U.UUID
                                left join PersonalAccountSingleInfo S on S.PersonalAccountUUID = P.UUID
                                left join Account A on A.UUID = P.<PERSON>rentUUID
                                left join Account AA on AA.UUID = A.ParentUUID
                                where TO_DAYS(P.ExpireTime) = TO_DAYS(NOW()) + :daysBefore 
                                and P.role = :role 
                                and P.Active = 1 and S.IsNewBilling = 1
                                and P.Special = 0 order by A.UUID");
        $sth->bindValue(':daysBefore', $daysBefore, PDO::PARAM_INT);
        $sth->bindValue(':role', ACCOUNT_ROLE_PERSONNAL_MAIN, PDO::PARAM_INT);
        $sth->execute();
        $expireList = $sth->fetchALL(PDO::FETCH_ASSOC);

        // 解密字段
        foreach ($expireList as $row => $item) {
            $expireList[$row]['Name'] = DataConfusion::getInstance()->decrypt($item['Name']);
            $expireList[$row]['Email'] = DataConfusion::getInstance()->decrypt($item['Email']);
            $expireList[$row]['MobileNumber'] = DataConfusion::getInstance()->decrypt($item['MobileNumber']);
        }
        return $expireList;
    }
}