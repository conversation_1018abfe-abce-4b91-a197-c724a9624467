<?php

require_once(__DIR__.'../../akcs_pdu.php');
require_once(__DIR__.'/../common/comm.php');
require_once(__DIR__.'/../common/define.php');
require_once(__DIR__.'/../common/snowflake.php');
require_once(__DIR__.'/../common/http_client.php');
require_once(__DIR__.'/../common/linker_define.php');
require_once(__DIR__.'/../common/smarthome_client.php');
require_once(__DIR__.'/../common/redis.php');

function OnRequestWeatherMsg($msg)
{
    try {
        // 兼容state_province为空的情况，state_province赋值为country
        $original_state_province = $msg["datas"]["state_province"];
        if (empty($msg["datas"]["state_province"])) {
            $msg["datas"]["state_province"] = $msg['datas']['country'];
        }

        // 地址缺失，不请求家居接口
        if (empty($msg["datas"]["city"] || $msg["datas"]["country"] || $msg["datas"]["state_province"])) {
            LOG_WARING("location is deficiency, ignore this request");
            return;
        }

        // 检查Redis中是否已经缓存了404状态
        if (isInvalidLocation($msg)) {
            return;
        }

        // 北亚云直接请求家居接口, 其他云走家居网关
        $weather = array();
        if (GATEWAY_NUM == GATEWAY_RUCLOUD) {
            $weather = requestWeatherByCloud($msg);
        } else {
            $weather = requestWeatherBySmartHomeGateway($msg);
        }

        if (is_array($weather) && $weather["success"] == true) {
            $data[0] = $msg['datas']['mac'] ?? '';
            $data[1] = $msg['datas']['country'] ?? '';
            $data[2] = $original_state_province ?? '';
            $data[3] = $msg['datas']['city'] ?? '';
            $data[4] = $weather['result']['aqi'] ?? '';
            $data[5] = $weather['result']['humidity'] ?? '';
            $data[6] = $weather['result']['temperature'] ?? '';
            $data[7] = $weather['result']['weather'] ?? '';
            $data[8] = $weather['result']['wind_speed'] ?? '';
            LOG_INFO("request weather data:" . json_encode($data));
            
            $weatherNotify = new CLinkerWeatherNotify();
            $weatherNotify->copy($data, $msg['project_type']);
        }
    } catch (Exception $e) {
        LOG_ERROR($e->getMessage());
    }
}

// 通过家居网关
function requestWeatherBySmartHomeGateway($msg)
{
    $smarthome = new SmartHomeClient(SMARTHOME_HTTP_GATE);
    $result = $smarthome->command_msg(LINKER_DEF_SMARTHOME_WEATHER, $msg["datas"]);
    
    if 
    if ($smarthome->httpCode() == 404) {
        cacheInvalidLoction($msg);
        return ["success" => false, "http_code" => 404];
    }
    return $result;
}

// 直接请求家居云
function requestWeatherByCloud($msg)
{
    $url = "https://" . SMARTHOME_DOMAIN . "/api/integration-entry/v1.0/invoke/integration-entry/method/akuvox/commands";

    $headers = [
        "Access-Token: " . SMARTHOME_TOKEN,
        "Content-Type: application/json",
        "Command-Name: get_weather_by_country_city"
    ];

    $traceId = IdCreate::createOnlyId();
    $data = [
        "id" => "$traceId",
        "command" => LINKER_DEF_SMARTHOME_WEATHER,
        "param" => [
            "country" => $msg["datas"]['country'],
            "state_province" => $msg["datas"]['state_province'], 
            "city" => $msg["datas"]['city']
        ]
    ];

    $httpClient = new HttpClient();
    $httpClient->headers($headers);
    $result = $httpClient->post($url, json_encode($data, JSON_UNESCAPED_UNICODE))->exec()->result();
    
    if ($httpClient->code() == 404) {
        cacheInvalidLoction($msg);
        return ["success" => false, "http_code" => 404];
    }
    return $result;
}

function getLocationNotFoundKey($msg)
{
    $country = $msg["datas"]['country'];
    $state_province = $msg["datas"]['state_province'];
    $city = $msg["datas"]['city'];
    return $country . ":" . $state_province . ":" . $city . ":404";
}

function isInvalidLocation($msg)
{
    $locationNotFountKey = getLocationNotFoundKey($msg);

    $redis_manager = new RedisManage();
    $redis = $redis_manager->getRedisInstance();
    $redis->select(REDIS_WEATHER);

    if ($redis->exists($locationNotFountKey)) {
        LOG_INFO("Found 404 cache for location: " . $locationNotFountKey . ", skipping API request");
        return true;
    }
    return false;
}

function cacheInvalidLoction($msg)
{
    $locationNotFountKey = getLocationNotFoundKey($msg);

    $redis_manager = new RedisManage();
    $redis = $redis_manager->getRedisInstance();
    $redis->select(REDIS_WEATHER);
    $redis->set($locationNotFountKey);
    
    LOG_INFO("Cached 404 status for location: " . $locationNotFountKey);
    return;
}