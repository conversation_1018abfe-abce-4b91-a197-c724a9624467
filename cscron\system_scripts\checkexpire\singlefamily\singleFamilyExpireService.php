<?php
require_once(dirname(__FILE__) . '/../email_notify_role.php');
require_once(dirname(__FILE__) . '/../check_expire_common_v4500.php');
require_once(dirname(__FILE__) . '/control/expireDataControl.php');
require_once(dirname(__FILE__) . '/handler/distributorHandler.php');
require_once(dirname(__FILE__) . '/handler/installerHandler.php');
require_once(dirname(__FILE__) . '/handler/enduserHandler.php');

const WEB_PER_NODE_UPDATE = 1000;

class SingleFamilyExpireService 
{
    private $db;
    private $medooDb;
    private $handlers = [];
    public $expireDataControl;
    
    public function __construct() {
        $this->medooDb = getMedooDb();
        $this->db = $this->medooDb->pdo;
        $this->expireDataControl = new SingleFamilyExpireDataControl($this->db, $this->medooDb);
        
        $this->handlers[EmailNotifyRole::SEND_TO_DIS] = new SingleFamilyDistributorHandler();
        $this->handlers[EmailNotifyRole::SEND_TO_INS] = new SingleFamilyInstallerHandler();
        $this->handlers[EmailNotifyRole::SEND_TO_ENDUSER] = new SingleFamilyEndUserHandler();
    }

    // 检查并发送单住户过期通知
    public function checkExpiration($daysBefore = [-1, 3, 5, 15]) {
        // 根据过期天数分别处理
        foreach ($daysBefore as $days) {
            $this->processExpireNotifications($days);
        }
    }

    /**
     * 处理特定天数的过期通知
     */
    private function processExpireNotifications($daysBefore) {
        LOG_INFO("开始处理单住户过期通知，天数: $daysBefore");
        
        // 查询所有过期数据: Resident APP，Premium Plan, Video Storage, ThirdParty Lock
        $expireData = $this->expireDataControl->getExpireData($daysBefore);
        LOG_INFO("单住户过期数据: " . json_encode($expireData));

        // 通知csconfig配置更新
        $this->notifyRefreshDevConfig($daysBefore, $expireData);

        // 获取过期的单住户列表
        $personalExpireInfoList = $this->expireDataControl->getPersonalExpireInfoList($expireData);
        LOG_INFO("单住户过期用户数据: " . json_encode($personalExpireInfoList));

        // 获取单住户的收费模式
        $personalChargeModeMap =  $this->expireDataControl->getPersonalChargeMode($personalExpireInfoList);
        LOG_INFO("单住户收费模式: " . json_encode($personalChargeModeMap));

        // 向各角色发送通知
        foreach ($this->handlers as $role => $handler) {
            // 判断角色当前天数是否需要通知
            if (!$this->roleNeedNotify($role, $daysBefore)) {
                continue;
            }
            LOG_INFO("处理角色 " . EmailNotifyRole::getRoleDescription($role) . " 的通知, 检查天数: $daysBefore");
    
            // 设置处理参数
            $handler->setExpireParams($daysBefore, $expireData, $personalExpireInfoList, $personalChargeModeMap, $this->expireDataControl);

            // 过滤掉不需要发送的数据
            $filteredExpireData = $handler->filterData();
            LOG_INFO("filteredExpireData: " . json_encode($filteredExpireData));

            // 获取接收者的uuid列表
            $recevierUUIDList = $handler->expireDataControl->getRecevierUUIDListByExpireData($role, $filteredExpireData);
            LOG_INFO("获取接收者的uuid列表: " . json_encode($recevierUUIDList));

            // 整理发送出去的邮件数据
            $emailDataMap = $handler->expireDataControl->getEmailDataMap($role, $filteredExpireData, $recevierUUIDList);
            LOG_INFO("发送出去的邮件数据: " . json_encode($emailDataMap));

            // 获取接收者的信息
            $recevierInfoMap = $handler->expireDataControl->getRecevierInfoMap($role, $recevierUUIDList, $emailDataMap);
            LOG_INFO("获取recevierInfoMap: " . json_encode($recevierInfoMap));

            // 发送邮件
            $handler->sendEmail($emailDataMap, $recevierInfoMap);
        }
    }

    // 通知csconfig配置更新
    private function notifyRefreshDevConfig($daysBefore, $expireData) {
        // daysBefore != -1，则不通知csconfig配置更新
        if ($daysBefore != -1) {
            return;
        }

        foreach ($expireData as $payItem => $dataList) {    
            foreach ($dataList as $data) {
                if ($payItem == EmailNotifyRule::PAY_ITEM_VIDEO_RECORD) {
                    webPersonalModifyNotify(WEB_PER_NODE_UPDATE, $data['PersonalAccount']);
                } elseif ($payItem == EmailNotifyRule::PAY_ITEM_FEATURE_PLAN) {
                    //只有开启高级功能的，才要刷房间下设备配置
                    if (IsEnablePersonalFeaturePlan($data['Switch'])) {
                        webPersonalModifyNotify(WEB_PER_UPDATE_MAC_CONFIG, $data['PersonalAccount']);
                    }
                }
            }
        }
    }

    private function roleNeedNotify($role, $daysBefore) {
        // enduser 通知3天和-1天的, 其他全通知
        if ($role == EmailNotifyRole::SEND_TO_ENDUSER) {
            return $daysBefore == -1 || $daysBefore == 3;
        } 
        return true;
    }
}

// 单住户过期检测服务
$singleFamilyExpireService = new SingleFamilyExpireService();
$singleFamilyExpireService->checkExpiration();