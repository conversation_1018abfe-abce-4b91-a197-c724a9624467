<?php

require_once(dirname(__FILE__) . '/pmAppControl.php');
require_once(dirname(__FILE__) . '/residentAppControl.php');
require_once(dirname(__FILE__) . '/premiumPlanControl.php');
require_once(dirname(__FILE__) . '/videoStorageControl.php');
require_once(dirname(__FILE__) . '/thirdPartyLockControl.php');
require_once(dirname(__FILE__) . '/expireDataCheckUtil.php');
require_once(dirname(__FILE__) . '/../model/commonQuery.php');
require_once(dirname(__FILE__) . '/../../../common/define.php');

class CommunityExpireDataControl
{
    public $commonQuery;
    private $payItemControls = [];
    
    public function __construct($db, $medooDb) {
        $this->commonQuery = new CommunityCommonQuery($db, $medooDb);
        $this->payItemControls[EmailNotifyRule::PAY_ITEM_USER_APP] = new CommunityResidentAppControl($db, $medooDb, $this->commonQuery);
        $this->payItemControls[EmailNotifyRule::PAY_ITEM_PM_APP] = new CommunityPmAppControl($db, $medooDb, $this->commonQuery);
        $this->payItemControls[EmailNotifyRule::PAY_ITEM_FEATURE_PLAN] = new CommunityPremiumPlanControl($db, $medooDb, $this->commonQuery);
        $this->payItemControls[EmailNotifyRule::PAY_ITEM_VIDEO_RECORD] = new CommunityVideoStorageControl($db, $medooDb, $this->commonQuery);
        $this->payItemControls[EmailNotifyRule::PAY_ITEM_THIRDLOCK_PUBLIC_AND_APT] = new CommunityThirdPartyLockControl($db, $medooDb, $this->commonQuery);
    }

    // 获取所有过期数据
    public function getExpireData($daysBefore) {
        $expireData = [];
        foreach ($this->payItemControls as $payItem => $control) {
            $expireData[$payItem] = $control->getExpireData($daysBefore);
        }
        return $expireData;
    }

    // 通过过期的列表，获取过期的社区列表
    public function getCommunityExpireInfoList($expireData)
    {
        $communityMap = [];

        foreach ($expireData as $payItem => $dataList) {
            foreach ($dataList as $data) {
                if (!isset($communityMap[$data['CommunityUUID']])) {
                    $communityMap[$data['CommunityUUID']] = [
                        'DisUUID' => $data['DisUUID'], 
                        'InsUUID' => $data['InsUUID'],
                        'ID' => $data['CommunityID'], 
                        'UUID' => $data['CommunityUUID'], 
                        'Community' => $data['Community']
                    ];
                }
            }
        }
        $communityExpireInfoList = array_values($communityMap);
        return $communityExpireInfoList;
    }

    // 获取社区的收费模式
    public function getCommunityChargeMode($communityExpireInfoList) {
        $communityChargeModeMap = [];
        foreach ($communityExpireInfoList as $data) {
            $communityChargeModeMap[$data['UUID']] = $this->commonQuery->getCommunityChargeModeByUUID($data['UUID']);
        }
        return $communityChargeModeMap;
    }

    // key是uuid, value是发送的数据
    public function getEmailDataMap($role, $expireData, $recevierUUIDList)
    {
        // 根据uuid列表，获取邮件数据
        $emailDataMap = [];
        foreach ($expireData as $payItem => $emailData) {
            foreach ($emailData as $data) {
                if ($role == EmailNotifyRole::SEND_TO_DIS) {
                    if (in_array($data['DisUUID'], $recevierUUIDList)) {
                        $emailDataMap[$data['DisUUID']][$payItem][] = $data;
                    }
                } elseif ($role == EmailNotifyRole::SEND_TO_INS) {
                    if (in_array($data['InsUUID'], $recevierUUIDList)) {
                        $emailDataMap[$data['InsUUID']][$payItem][] = $data;
                    }
                } elseif ($role == EmailNotifyRole::SEND_TO_PM) {
                    if (array_key_exists($data['CommunityUUID'], $recevierUUIDList)) {
                        $pmUUIDList = $recevierUUIDList[$data['CommunityUUID']];
                        foreach ($pmUUIDList as $pmUUID) {
                            $emailDataMap[$pmUUID][$payItem][] = $data;
                        }
                    }
                } elseif ($role == EmailNotifyRole::SEND_TO_ENDUSER) {
                    if (!empty($data['PersonalAccountUUID']) && in_array($data['PersonalAccountUUID'], $recevierUUIDList)) {
                        $emailDataMap[$data['PersonalAccountUUID']][$payItem][] = $data;
                    }
                }
            }
        }
        return $emailDataMap;
    }

    public function getRecevierUUIDListByExpireData($role, $expireData) {
        $uuidList = [];
        foreach ($expireData as $payItem => $dataList) {
            if (empty($dataList)) {
                continue;
            }

            foreach ($dataList as $data) {
                if ($role == EmailNotifyRole::SEND_TO_DIS) {
                    $uuidList[] = $data['DisUUID'];
                } elseif ($role == EmailNotifyRole::SEND_TO_INS) {
                    $uuidList[] = $data['InsUUID'];
                } elseif ($role == EmailNotifyRole::SEND_TO_PM) {
                    // 获取社区UUID列表
                    $uuidList[] = $data['CommunityUUID'];
                } elseif ($role == EmailNotifyRole::SEND_TO_ENDUSER) {
                    $uuidList[] = $data['PersonalAccountUUID'];
                }
            }
        }
        $uniqueUUIDList = array_unique($uuidList);

        if ($role != EmailNotifyRole::SEND_TO_PM) {
            return $uniqueUUIDList;
        }

        // 获取每个社区的对应的pmuuid列表
        $communityPmUUIDMap = [];
        foreach ($uniqueUUIDList as $communityUUID) {
            $pmUUIDs = array_column($this->commonQuery->getCommunityPMUUIDListByCommunityUUID($communityUUID), 'UUID');
            if (!empty($pmUUIDs)) {
                $communityPmUUIDMap[$communityUUID] = $pmUUIDs;
            }
        }
        return $communityPmUUIDMap;
    }

    // 获取接收者的dis uuid map
    private function getRecevierDisUUIDMap($emailDataMap) {
        $recevierDisUUIDMap = [];
        foreach ($emailDataMap as $recevierUUID => $dataGroups) {
            foreach ($dataGroups as $payType => $dataList) {
                if (!empty($dataList) && isset($dataList[0]['DisUUID'])) {
                    $recevierDisUUIDMap[$recevierUUID] = $dataList[0]['DisUUID'];
                    break;
                }
            }
        }
        return $recevierDisUUIDMap;
    }

    // 获取BillingInfo的email 
    private function getBillingInfo($role, $uuid) {
        $billingInfo = [];
        if ($role == EmailNotifyRole::SEND_TO_DIS || $role == EmailNotifyRole::SEND_TO_INS) {
            $billingInfo = $this->commonQuery->getBillingInfoByAccountUUID($uuid);
        } elseif ($role == EmailNotifyRole::SEND_TO_PM) {
           $billingInfo = $this->commonQuery->getPmBillingInfoByAccountUUID($uuid);
           if ($billingInfo !== null && is_array($billingInfo)) {
               $billingInfo['Name'] = $billingInfo['FirstName'] . " " . $billingInfo['LastName'];
           }
        } elseif ($role == EmailNotifyRole::SEND_TO_ENDUSER) {
            $billingInfo = $this->commonQuery->getBillingInfoByPersonalAccountUUID($uuid);
        }
        
        return $billingInfo !== null ? $billingInfo : [];
    }

    private function getDisOemName($disAccounUUID)
    {
        $oemType = $this->commonQuery->getDisOemType($disAccounUUID);
        return DIS_OEM_NAME[$oemType];
    }

    // 获取接收者的邮箱,语言,oem
    function getRecevierInfoMap($role, $emailDataMap)
    {
        $recevierInfoMap = [];
        $recevierUUIDList = array_keys($emailDataMap);
        $recevierDisUUIDMap = $this->getRecevierDisUUIDMap($emailDataMap);

        foreach ($recevierUUIDList as $recevierUUID) {
            // 跳过空的UUID
            if (empty($recevierUUID)) {
                continue;
            }
            
            $billingInfo = $this->getBillingInfo($role, $recevierUUID);
            
            // 确保billingInfo不为空且包含必要的字段
            if (empty($billingInfo) || !is_array($billingInfo)) {
                LOG_INFO("getBillingInfo returned empty result for role: $role, uuid: $recevierUUID");
                continue;
            }
            
            $oemName = $this->getDisOemName($recevierDisUUIDMap[$recevierUUID]);

            $recevierInfoMap[$recevierUUID] = [
                'oem' => $oemName,
                'email' => $billingInfo['Email'] ?? '',
                'user' => $billingInfo['Account'] ?? '',
                'language' => $billingInfo['Language'] ?? '',
                'loginAccount' => $billingInfo['Account'] ?? ''
            ];

            if ($role == EmailNotifyRole::SEND_TO_PM || $role == EmailNotifyRole::SEND_TO_ENDUSER) {
                $recevierInfoMap[$recevierUUID]['user'] = $billingInfo['Name'];
            }
        }
        return $recevierInfoMap;
    }
}