<?php

require_once(dirname(__FILE__) . '/expireDataCheckUtil.php');
require_once(dirname(__FILE__) . '/../model/premiumPlanQuery.php');

class CommunityPremiumPlanControl extends CommunityExpireDataCheckUtil
{
    public $commonQuery;
    private $premiumPlanQuery;

    public function __construct($db, $medooDb, $commonQuery)
    {
        parent::__construct($db, $medooDb);
        $this->commonQuery = $commonQuery;
        $this->premiumPlanQuery = new CommunityPremiumPlanQuery($db, $medooDb);
    }

    public function getExpireData($daysBefore)
    {
        // 获取过期数据
        $expireData = $this->premiumPlanQuery->getExpireData($daysBefore);

        // 过滤掉迁移dis的社区
        $expireData = $this->filterMigrateDistributor($expireData);

        // 过滤掉自动扣费的社区
        $expireData = $this->filterAutoPayCommunity($expireData);   

        return $expireData;
    }

    private function filterAutoPayCommunity($expireData)
    {
        $filteredData = [];
        foreach ($expireData as $data) {
            if (!$this->commonQuery->IsCommunityEnableAutoPay($data['CommunityUUID'], AUTO_PAY_ORDER_TYPE_COMMUNITY_FEATURE_PLAN)
            && !$this->commonQuery->IsCommunityEnableMixAutoPay($data['CommunityUUID'], AUTO_PAY_ORDER_TYPE_COMMUNITY_FEATURE_PLAN)) {
                $filteredData[] = $data;
            }
        }
        return $filteredData;
    }
}