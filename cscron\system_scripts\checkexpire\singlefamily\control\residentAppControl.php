<?php
require_once(dirname(__FILE__) . '/expireDataCheckUtil.php');
require_once(dirname(__FILE__) . '/../model/residentAppQuery.php');

class SingleFamilyResidentAppControl extends SingleFamilyExpireDataCheckUtil
{
    public $commonQuery;
    private $residentAppQuery;

    public function __construct($db, $medooDb, $commonQuery)
    {
        parent::__construct($db, $medooDb);
        $this->commonQuery = $commonQuery;
        $this->residentAppQuery = new SingleFamilyResidentAppQuery($db, $medooDb);
    }

    public function getExpireData($daysBefore)
    {
        // 获取过期数据
        $expireData = $this->residentAppQuery->getExpireData($daysBefore);

        // 过滤掉迁移dis
        $expireData = $this->filterMigrateDistributor($expireData);

        // 过滤掉自动扣费的用户
        $expireData = $this->filterResidentAppAutoPayUser($expireData);
        LOG_INFO("单住户App过期数据 : " . json_encode($expireData));

        return $expireData;
    }

    // 过滤掉自动扣费的用户
    private function filterResidentAppAutoPayUser($expireData)
    {
        $filteredData = [];
        foreach ($expireData as $row => $item) {
            if (!$this->commonQuery->IsUserEnableAutoPay($item['PersonalAccountUUID'], AUTO_PAY_ORDER_TYPE_SINGLE)) {
                $filteredData[] = $item;
            }
        }
        return $filteredData;
    }
}