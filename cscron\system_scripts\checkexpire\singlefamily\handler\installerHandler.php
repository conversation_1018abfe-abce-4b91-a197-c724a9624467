<?php
require_once __DIR__ . '/../../../common/email_common.php';

class SingleFamilyInstallerHandler 
{
    private $daysBefore;
    private $expireDataMap;
    private $personalExpireInfoList;
    private $personalChargeModeMap;
    public $expireDataControl;

    public function __construct() {
    }
    
    public function setExpireParams($daysBefore, $expireDataMap, $personalExpireInfoList, $personalChargeModeMap, $expireDataControl) {
        $this->daysBefore = $daysBefore;
        $this->expireDataMap = $expireDataMap;
        $this->personalExpireInfoList = $personalExpireInfoList;
        $this->personalChargeModeMap = $personalChargeModeMap;
        $this->expireDataControl = $expireDataControl;
    }

    public function filterData() {
        // 判断社区的付费模式 是否要发送给Installer
        $filteredExpireData = $this->filterPersonalChargeMode();
        return $filteredExpireData;
    }

    private function filterPersonalChargeMode()
    {
         // 判断单住户的付费模式 是否要发送给Installer
         $payTypeList = array(
            EmailNotifyRule::PAY_ITEM_USER_APP,
            EmailNotifyRule::PAY_ITEM_FEATURE_PLAN,
            EmailNotifyRule::PAY_ITEM_VIDEO_RECORD,
            EmailNotifyRule::PAY_ITEM_THIRDLOCK_PUBLIC_AND_APT,
        );

        // 获取每种付费类型发送给Ins的用户列表
        $payTypePersonalAccountUUIDMap = array(); 
        foreach ($payTypeList as $payType) {
            foreach ($this->personalExpireInfoList as $personalAccountInfo) {
                $personalChargeMode = $this->personalChargeModeMap[$personalAccountInfo['PersonalAccountUUID']];
                if ($personalChargeMode && PersonalCheck::needNotifyIns($payType, $personalChargeMode, $this->daysBefore)) {
                    $payTypePersonalAccountUUIDMap[$payType][] = $personalAccountInfo['PersonalAccountUUID'];
                }
            }
        }

        // 获取每种付费类型发送给Ins的用户列表
        $filteredExpireData = array();
        foreach ($this->expireDataMap as $payType => $expireData) {
            foreach ($expireData as $expireInfo) {
                if (isset($payTypePersonalAccountUUIDMap[$payType]) && in_array($expireInfo['PersonalAccountUUID'], $payTypePersonalAccountUUIDMap[$payType])) {
                    $filteredExpireData[$payType][] = $expireInfo;
                }
            }
        }
        return $filteredExpireData;
    }

    // ins下同一个时区的用户一起发送
    private function getTimeZoneEmailDataMap($insEmailData) {
        $timeZoneEmailDataMap = array();
        foreach ($insEmailData as $payType => $dataList) {
            foreach ($dataList as $data) {
                $timeZonePrefix = explode(' ', $data['TimeZone'])[0];
                $timeZoneEmailDataMap[$timeZonePrefix][$payType][] = $data;
                LOG_INFO("ins getTimeZoneEmailDataMap data: " . json_encode($data));
            }
        }
        return $timeZoneEmailDataMap;
    }

    // 发送邮件给Ins
    public function sendEmail($emailDataMap, $recevierInfoMap) {

        foreach ($emailDataMap as $insUUID => $insEmailData) {
            $insEmail = $recevierInfoMap[$insUUID]['email'];
            if (empty($insEmail)) {
                LOG_INFO("insEmail is empty, skip, disUUID: " . $insUUID);
                continue;
            }

            $showPayLink = SHOW_PAYLINK_NONE;
            if ($this->insShowPayLink($insUUID)) {
                $showPayLink = SHOW_PAYLINK_INSTALLER;
            }

            // ins下同一个时区的用户一起发送
            $timezoneEmailDataMap = $this->getTimeZoneEmailDataMap($insEmailData);
            foreach ($timezoneEmailDataMap as $timeZone => $emailData) {
                
                $emailInfo['email'] = $insEmail;
                $emailInfo["web_ip"] = WEB_DOMAIN;
                $emailInfo['before'] = $this->daysBefore;
                $emailInfo['is_show_paylink'] = $showPayLink;
                $emailInfo['oem'] = $recevierInfoMap[$insUUID]['oem'];
                $emailInfo['user'] = $recevierInfoMap[$insUUID]['user'];
                $emailInfo['language'] = $recevierInfoMap[$insUUID]['language'];
                $emailInfo['installer_id'] = $recevierInfoMap[$insUUID]['installerID'];
                $emailInfo['login_account'] = $recevierInfoMap[$insUUID]['loginAccount'];
                
                if ($this->daysBefore == -1) {
                    $emailInfo['email_type'] = "single_family_has_expired";
                } else {
                    $emailInfo['email_type'] = "single_family_will_expire";
                }

                $emailInfo['list'] = $emailData;
                sendEmailNotifyNew($emailInfo);
            }
        }
    }

    public function insShowPayLink($insUUID)
    {
        $insAccountInfo = $this->expireDataControl->commonQuery->getAccountInfoByAccountUUID($insUUID);
        return EmailNotifyRule::IfInsHasPayPermission($insAccountInfo['ChargeMode']);
    }
}