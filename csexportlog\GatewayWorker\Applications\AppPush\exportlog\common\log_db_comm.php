<?php
require_once(__DIR__.'/define.php');
require_once(__DIR__.'/comm.php');
require_once(__DIR__.'/../akcs_monitor.php');
require_once(__DIR__.'/consistentHash.php');

const PERSONAL_CAPTURE_KEY = "ID,MAC,MngAccountID,DevType,MngType,SipAccount,Location,PicName,PicUrl,SPicUrl,CaptureAction,Initiator,CaptureTime,Response,Status,Node,CaptureType,KeyNum,RoomNum,UnitID,Relay,SecurityRelay,AccessMode,UserType,OfficeCompanyUUID,ProjectUUID,DoorNameList ";


function getProjectUUID($community_id)
{
    $db = getDB();
    $sth = $db->prepare("select UUID from Account where ID = :id");
    $sth->bindParam(':id', $community_id, PDO::PARAM_INT);
    $ret = $sth->execute();
    $data = $sth->fetch(PDO::FETCH_ASSOC);
    return $data['UUID'];
}

function GetProjectName($project_uuid)
{
    // 3.查找公司名称 （注意：这里是从AKCS数据库查找的）
    $akcs_db = getDB();
    $sth = $akcs_db->prepare("select Location from Account where UUID=:ProjectUUID");
    $sth->bindParam(':ProjectUUID', $project_uuid, PDO::PARAM_STR);
    $sth->execute();
    $company_info = $sth->fetch(PDO::FETCH_ASSOC)["Location"];
    return $company_info;
}

function getAccountIDByUUID($uuid)
{
    $db = getDB();
    $sth = $db->prepare("select ID from Account where UUID = :uuid");
    $sth->bindParam(':uuid', $uuid, PDO::PARAM_STR);
    $ret = $sth->execute();
    $data = $sth->fetch(PDO::FETCH_ASSOC);
    return $data['ID'];
}

function getLogDeliveryInfo($table_name)
{
    $db = getLOGDB();
    $sth = $db->prepare("select Delivery,LastDelivery,MaxSaveMonth,unix_timestamp(DeliveryTime) as DeliveryTime FROM LogSlice where LogTableName = :table_name");
    $sth->bindParam(':table_name', $table_name, PDO::PARAM_STR);
    $ret = $sth->execute();
    $data = $sth->fetch(PDO::FETCH_ASSOC);
    $data['LogTableName'] = $table_name;
    return $data;
}

function getLogDbTableIndex($project_uuid, $delivery)
{
    $hash = sprintf('%u', crc32($project_uuid));
    return $hash%$delivery;
}

//日志记录中途是否分片
function IsDeliveredWhileRecord($table_slice_info)
{
    $db = getLOGDB();
    $sth = $db->prepare("select unix_timestamp(NOW() -interval 30*:max_save_month day) < :delivery_time as Flag");
    $sth->bindParam(':max_save_month', $table_slice_info['MaxSaveMonth'], PDO::PARAM_STR);
    $sth->bindParam(':delivery_time', $table_slice_info['DeliveryTime'], PDO::PARAM_STR);
    $ret = $sth->execute();
    $result = $sth->fetch(PDO::FETCH_ASSOC);
    return  $result['Flag'];
}

function getSqlTables($basic_table, $project_uuid, &$log_tables)
{
    global $capture_slice_info;
    global $motion_slice_info;
    global $call_slice_info;

    $table_slice_info = array();
    if ($basic_table == "PersonalCapture"){
        $table_slice_info = $capture_slice_info;
    } else if ($basic_table == "PersonalMotion"){
        $table_slice_info = $motion_slice_info;
    } else if ($basic_table == "CallHistory"){
        $table_slice_info = $call_slice_info;
    }

    $last_delivery = $table_slice_info['LastDelivery'];
    //当前时间 减去 $DeliveryTime时间 是否小于 $MaxSaveMonth
    $flag = IsDeliveredWhileRecord($table_slice_info);
    $index = getLogDbTableIndex($project_uuid, $table_slice_info['Delivery']);

    if ($flag){
        //如LOG.PersonalCapture_2, LOG.PersonalCapture_2_202303, LOG.PersonalCapture_5_202303, ...
        $last_delivery_index = getLogDbTableIndex($project_uuid, $table_slice_info['LastDelivery']);
        $log_tables = [$basic_table.'_'.$index, $basic_table.'_'.$last_delivery_index];
        for ($i=1; $i<=$table_slice_info['MaxSaveMonth']; $i++){
            $log_month = date("Ym", strtotime("first day of -$i month"));
            array_push($log_tables, $basic_table.'_'.$index.'_'.$log_month);
            array_push($log_tables, $basic_table.'_'.$last_delivery_index.'_'.$log_month);
        }
    } else  {
        //如LOG.PersonalCapture_2, LOG.PersonalCapture_2_202303, LOG.PersonalCapture_2_202302, ...
        array_push($log_tables, $basic_table.'_'.$index);
        for ($i=1; $i<=$table_slice_info['MaxSaveMonth']; $i++){
            $log_month = date("Ym", strtotime("first day of -$i month"));
            array_push($log_tables, $basic_table.'_'.$index.'_'.$log_month);
        }
    }

}

//计算db节点
function getLogDbNode($project_uuid)
{
    $dbNode = 0;
    //根据LOG_DATABASEIP来确定db总数量
    $dbNum = count(explode(";",LOG_MYSQL_DB_IP));
    if($dbNum > 1)
    {
        //通过一致性hash计算
        $ch = new \util\consistentHash\DbConsistentHash();
        $ch->initDbNumList($dbNum); 
        $dbNode = $ch->getDbNumByKey($project_uuid);
    }
    return $dbNode;
}

function LOGDBTableExist($logtable)
{
    $db = getLOGDB();
    $sth = $db->prepare("show tables like \"$logtable\";");
    $ret = $sth->execute();
    $data = $sth->fetch(PDO::FETCH_ASSOC);
    if ($data) {
        return 1;
    }
    return 0;
}

function getPmExportLogRecordByUUID($record_uuid)
{
    $db = getDB();
    $sth = $db->prepare("select UUID,AccountUUID,TraceID,LogType,ExportType,ExpiredTime,ExportTime,DownloadUrl FROM PmExportLogRecord where UUID = :record_uuid");
    $sth->bindParam(':record_uuid', $record_uuid, PDO::PARAM_STR);
    $ret = $sth->execute();
    $data = $sth->fetch(PDO::FETCH_ASSOC);
    return $data;
}

function getAdminExportLogRecordByUUID($record_uuid)
{
    $db = getDB();
    $sth = $db->prepare("select UUID,OfficeCompanyUUID,TraceID,LogType,ExportType,ExpiredTime,ExportTime,DownloadUrl FROM AdminExportLogRecord where UUID = :record_uuid");
    $sth->bindParam(':record_uuid', $record_uuid, PDO::PARAM_STR);
    $ret = $sth->execute();
    $data = $sth->fetch(PDO::FETCH_ASSOC);
    return $data;
}


function getDisOemNameByProjectID($project_id)
{
    $db = getDB();
    $sql = "SELECT d.OemType 
            FROM Account a 
            INNER JOIN Account dis ON a.ParentUUID = dis.UUID 
            INNER JOIN DistributorInfo d ON dis.Account = d.Account 
            WHERE a.ID = :project_id";
    
    $sth = $db->prepare($sql);
    $sth->bindParam(':project_id', $project_id, PDO::PARAM_INT);
    $ret = $sth->execute();
    
    $data = $sth->fetch(PDO::FETCH_ASSOC);
    if ($data && isset($data['OemType'])) 
    {
        $oem_index = intval($data['OemType']);
        // 确保索引在有效范围内
        if ($oem_index < 0 || $oem_index >= count(DIS_OEM_NAME)) 
        {
            // 日志记录：OEM索引超出范围，使用默认OEM
            error_log("OEM index is out of bounds, oem_index: " . $oem_index . ", use default oem: Akuvox");
            $oem_index = 0;
        }
        return DIS_OEM_NAME[$oem_index];
    }
    
    
    // 如果查询失败或没有找到数据，返回默认OEM名称
    return OEM_NAME_AKUVOX;
}

function getDisOemNameByProjectUUID($project_uuid)
{
    $db = getDB();
    $sql = "SELECT d.OemType 
            FROM Account a 
            INNER JOIN Account dis ON a.ParentUUID = dis.UUID 
            INNER JOIN DistributorInfo d ON dis.Account = d.Account 
            WHERE a.UUID = :project_uuid";
    
    $sth = $db->prepare($sql);
    $sth->bindParam(':project_uuid', $project_uuid, PDO::PARAM_STR);
    $ret = $sth->execute();
    $data = $sth->fetch(PDO::FETCH_ASSOC);
    if ($data && isset($data['OemType'])) 
    {
        $oem_index = intval($data['OemType']);
        // 确保索引在有效范围内
        if ($oem_index < 0 || $oem_index >= count(DIS_OEM_NAME)) 
        {
            // 日志记录：OEM索引超出范围，使用默认OEM
            error_log("OEM index is out of bounds, oem_index: " . $oem_index . ", use default oem: Akuvox");
            $oem_index = 0;
        }
        return DIS_OEM_NAME[$oem_index];
    }
    
    // 如果查询失败或没有找到数据，返回默认OEM名称
    return OEM_NAME_AKUVOX;
}

