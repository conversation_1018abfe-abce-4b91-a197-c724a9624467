<?php
require_once(dirname(__FILE__) . '/../email_notify_role.php');
require_once(dirname(__FILE__) . '/../check_expire_common_v4500.php');
require_once(dirname(__FILE__) . '/control/expireDataControl.php');
require_once(dirname(__FILE__) . '/handler/distributorHandler.php');
require_once(dirname(__FILE__) . '/handler/installerHandler.php');
require_once(dirname(__FILE__) . '/handler/propertyManegeHandler.php');
require_once(dirname(__FILE__) . '/handler/enduserHandler.php');

const WEB_COMM_UPDATE_CONFIG_AND_CONTACT = 2110;

// 社区过期检测服务
$communityExpireService = new CommunityExpireService();
$communityExpireService->checkExpiration();

class CommunityExpireService {
    private $db;
    private $medooDb;
    private $handlers = [];
    private $expireDataControl;
    
    public function __construct() {
        $this->medooDb = getMedooDb();
        $this->db = $this->medooDb->pdo;
        $this->expireDataControl = new CommunityExpireDataControl($this->db, $this->medooDb);
        
        $this->handlers[EmailNotifyRole::SEND_TO_DIS] = new CommunityDistributorHandler();
        $this->handlers[EmailNotifyRole::SEND_TO_INS] = new CommunityInstallerHandler();
        $this->handlers[EmailNotifyRole::SEND_TO_PM] = new CommunityPropertyManegeHandler();
        $this->handlers[EmailNotifyRole::SEND_TO_ENDUSER] = new CommunityEndUserHandler();
    }

    // 检查并发送社区过期通知
    public function checkExpiration($daysBefore = [15, 5, 3, -1]) {
        // 根据过期天数分别处理
        foreach ($daysBefore as $days) {
            $this->processExpireNotifications($days);
        }
    }

    /**
     * 处理特定天数的过期通知
     */
    private function processExpireNotifications($daysBefore) {
        LOG_INFO("开始处理社区过期通知，天数: $daysBefore");
        
        // 查询所有过期数据: Resident APP，PM APP，Premium Plan, Video Storage, ThirdParty Lock
        $expireData = $this->expireDataControl->getExpireData($daysBefore);
        LOG_INFO("社区过期数据: " . json_encode($expireData));

        // 通知csconfig配置更新
        $this->notifyRefreshDevConfig($daysBefore, $expireData);

        // 获取过期的社区列表   
        $communityExpireInfoList = $this->expireDataControl->getCommunityExpireInfoList($expireData);
        LOG_INFO("过期的社区列表: " . json_encode($communityExpireInfoList));

        // 获取社区的收费模式
        $communityChargeModeMap =  $this->expireDataControl->getCommunityChargeMode($communityExpireInfoList);
        LOG_INFO("社区收费模式: " . json_encode($communityChargeModeMap));

        // 向各角色发送通知
        foreach ($this->handlers as $role => $handler) {
            // 判断角色当前天数是否需要通知
            if (!$this->roleNeedNotify($role, $daysBefore)) {
                continue;
            }
            LOG_INFO("处理角色 " . EmailNotifyRole::getRoleDescription($role) . " 的通知, 检查天数: $daysBefore");
            
            // 设置处理参数
            $handler->setExpireParams($daysBefore, $expireData, $communityExpireInfoList, $communityChargeModeMap, $this->expireDataControl);

            // 过滤掉不需要发送的数据
            $filteredExpireData = $handler->filterData();
            LOG_INFO("过滤掉不需要发送的数据: " . json_encode($filteredExpireData));

            // 获取接收者的uuid列表
            $recevierUUIDList = $handler->expireDataControl->getRecevierUUIDListByExpireData($role, $filteredExpireData);
            LOG_INFO("获取接收者的uuid列表: " . json_encode($recevierUUIDList));
            
            // 整理发送出去的邮件数据
            $emailDataMap = $this->expireDataControl->getEmailDataMap($role, $filteredExpireData, $recevierUUIDList);
            LOG_INFO("发送出去的邮件数据: " . json_encode($emailDataMap));

            // 获取接收者的信息
            $recevierInfoMap = $this->expireDataControl->getRecevierInfoMap($role, $emailDataMap);
            LOG_INFO("获取recevierInfoMap: " . json_encode($recevierInfoMap));

            // 发送邮件
            $handler->sendEmail($emailDataMap, $recevierInfoMap);
        }
    }

    // 通知csconfig配置更新（视频存储的要刷配置）
    private function notifyRefreshDevConfig($daysBefore, $expireData) {

        // 如果remainDays != -1，则不通知csconfig配置更新
        if ($daysBefore != -1) {
            return;
        }

        foreach ($expireData as $payItem => $dataList) {    
            // 刷视频存储的配置
            if ($payItem == EmailNotifyRule::PAY_ITEM_VIDEO_RECORD) {
                if (empty($dataList)) {
                    continue;
                }

                $communityIDList = array();
                foreach ($dataList as $data) {
                    $communityIDList[] = $data['CommunityID'];
                }
                
                // 去除重复的社区ID
                $communityIDList = array_unique($communityIDList);
                foreach ($communityIDList as $communityID) {
                    webCommunityModifyNotify(WEB_COMM_UPDATE_CONFIG_AND_CONTACT, "", "", $communityID);
                }
            } elseif ($payItem == EmailNotifyRule::PAY_ITEM_USER_APP) {
                if (empty($dataList)) {
                    continue;
                }
                // 刷sip配置
                foreach ($dataList as $data) {
                    ModifyExpireAccountSipInfo($data['PersonalAccount'], $data['PersonalAccountUUID']);
                }
            }
        }
    }

    private function roleNeedNotify($role, $daysBefore) {
        // enduser 通知3天和-1天的, 其他全通知
        if ($role == EmailNotifyRole::SEND_TO_ENDUSER) {
            return $daysBefore == -1 || $daysBefore == 3;
        } 
        return true;
    }
}