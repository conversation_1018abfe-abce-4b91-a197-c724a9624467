<?php

require_once(dirname(__FILE__) . '/snowflake.php');
require_once(dirname(__FILE__) . '/http_client.php');
require_once(dirname(__FILE__) . '/../akcs_log.php');
require_once(dirname(__FILE__) . '/../akcs_monitor.php');
require_once(dirname(__FILE__) . '/comm.php');
require_once(dirname(__FILE__) . '/linker_define.php');

class SmartHomeClient
{
    private $urlhead;
    private $httpCode;
    public function __construct($urlhead)
    {
        $this->urlhead = $urlhead;
    }

    private function update_url_head($url_head)
    {
        $this->urlhead = $url_head;
    }

    private function request($id, $param, $url, $is_notice = 1)
    {
        $traceid = IdCreate::createOnlyId();
        $timestamp = time();
        $body = array();
        $body["trace_id"] = strval($traceid);
        $body["timestamp"] = $timestamp;

        $data = array();
        if ($is_notice) {
            $data["notice"] = $id;
        } else {
            $data["command"] = $id;
        }
        $data["id"] = strval($traceid);
        $data["timestamp"] = getUnixTimestamp();
        $data["param"] = $param;

        $body["data"] = $data;

        try {
            $data_string = json_encode($body);
            $http = new HttpClient();
            $head[0] = "Content-Type: application/json";
            $head[1] = "Content-Length: ".strlen($data_string);
            $http->headers($head);

            $this->httpCode = $http->post($url, $data_string)->exec()->code();
            $ret = $http->result();

            if ($this->httpCode == 200 && $ret["success"] == true) {
                LOG_INFO("push smarthome ok.data:$data_string");
                return $ret;
            } else {
                $retmsg = json_encode($ret);
                // 天气预报不推送告警
                if ($data["command"] != LINKER_DEF_SMARTHOME_WEATHER) {
                    LOG_ERROR("cslinker push smarthome error, data:$data_string, ret msg:$retmsg");
                } else {
                    LOG_INFO("push smarthome error.data:$data_string, ret:$retmsg");
                }
            }
        } catch (Exception $e) {
            $msg = json_encode($body);
            LOG_ERROR("push smarthome exception error.data:$msg". $e->getMessage());
        }
    }
    public function command_msg($notice_type, $param)
    {
        $url = $this->urlhead."/command/handle";
        return $this->request($notice_type, $param, $url, 0);
    }

    public function notice_msg($notice_type, $param)
    {
        $url = $this->urlhead."/notice/handle";
        return $this->request($notice_type, $param, $url);
    }

    public function httpCode()
    {
        $code = $this->httpCode;
        return $code;
    }
}
