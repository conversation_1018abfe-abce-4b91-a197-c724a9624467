<?php

/**
 * This file is part of workerman.
 *
 * Licensed under The MIT License
 * For full copyright and license information, please see the MIT-LICENSE.txt
 * Redistributions of files must retain the above copyright notice.
 *
 * <AUTHOR>
 * @copyright walkor<<EMAIL>>
 * @link http://www.workerman.net/
 * @license http://www.opensource.org/licenses/mit-license.php MIT License
 */
use Workerman\Worker;
use Workerman\WebServer;
use GatewayWorker\Gateway;
use GatewayWorker\BusinessWorker;
use Workerman\Autoloader;
require_once(__DIR__.'/../common/define.php');
require_once(__DIR__.'/../route.php');
/*
BusinessWorker类其实也是基于基础的Worker开发的。BusinessWorker是运行业务逻辑的进程，
BusinessWorker收到Gateway转发来的事件及请求时会默认调用Events.php中的onConnect onMessage onClose方法处理事件及数据，
开发者正是通过实现这些回调控制业务及流程。
*/

// bussinessWorker 进程
$worker = new BusinessWorker();
// worker名称
$worker->name = 'LinkBusinessWorker';
// bussinessWorker进程数量
$worker->count = BUSINESSWORK_NUM;
// 服务注册地址
$worker->registerAddress = '127.0.0.1:1238';

//在Worker::run() 中，会调用用户嵌入的该函数
$worker->onWorkerStart = function ($worker) {
    //global $email_Config; //email 配置
    //$email_Config = parse_ini_file(PUSH_CONF_ROOT_DIR .'/email.conf', true);

    // global $ios_Config; //ios 配置
    // $ios_Config = parse_ini_file(PUSH_CONF_ROOT_DIR .'/ios.conf', true);
    global $g_route;
    $g_route = create_routing();

};

Worker::$logFile = LOG_FILE_PATH;
// 如果不是在根目录启动，则运行runAll方法
if (!defined('GLOBAL_START')) {
    Worker::runAll();
}
