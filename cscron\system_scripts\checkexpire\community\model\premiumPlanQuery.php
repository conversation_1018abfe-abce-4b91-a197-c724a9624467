<?php

class CommunityPremiumPlanQuery 
{
    private $db;
    private $medooDb;

    public function __construct($db, $medooDb) {
        $this->db = $db;
        $this->medooDb = $medooDb;
    }

    public function getExpireData($daysBefore)
    {
        $sth = $this->db->prepare("select C.AccountUUID as CommunityUUID, A.Location as Community, A.TimeZone, A.ID as CommunityID, AA.UUID as InsUUID, AA.Account as InsAccount, 
                                    AAA.UUID as DisUUID, AAA.Account as DisAccount, C.FeatureExpireTime
                                    from CommunityInfo C
                                    left join Account A on A.UUID = C.AccountUUID 
                                    left join Account AA on AA.ManageGroup = A.ManageGroup
                                    left join Account AAA on AA.ParentUUID = AAA.UUID 
                                where TO_DAYS(C.FeatureExpireTime) = TO_DAYS(NOW()) + :daysBefore and AA.Grade = 22");
        $sth->bindValue(':daysBefore', $daysBefore, PDO::PARAM_INT);
        $sth->execute();
        $expireList = $sth->fetchALL(PDO::FETCH_ASSOC);
        return $expireList;
    }
}