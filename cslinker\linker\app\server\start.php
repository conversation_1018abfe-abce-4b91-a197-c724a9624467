<?php
/**
 * This file is part of workerman.
 *
 * Licensed under The MIT License
 * For full copyright and license information, please see the MIT-LICENSE.txt
 * Redistributions of files must retain the above copyright notice.
 *
 * <AUTHOR>
 * @copyright walkor<<EMAIL>>
 * @link http://www.workerman.net/
 * @license http://www.opensource.org/licenses/mit-license.php MIT License
 */
use Workerman\Worker;
use Workerman\Autoloader;
use Workerman\Connection\TcpConnection;
use Workerman\Protocols\Http\Request;

require_once __DIR__ . '../../../vendor/autoload.php';  //通过引用autoload.php文件的方式将用到的所有类文件加载进来
require_once(__DIR__.'/../common/define.php');
require_once(__DIR__.'/../common/comm.php');
require_once(__DIR__.'/../akcs_timer.php');
require_once(__DIR__.'/../akcs_kafka.php');

$consumer = new Worker();
$consumer->count = KAFKA_CONSUMER_NUM;

$consumer->onWorkerStart = function (Worker $consumer) {
    while (1) {
        $consumer = new AkKafkaConsumer(KAFKA_CSLINKER_TOPIC, KAFKA_CSLINKER_CONSUMER);
        $consumer->Start();
    }
};

$akcsTimer = new Worker();
$akcsTimer->count = 1;

$akcsTimer->onWorkerStart = function (Worker $akcsTimer) {
    $timer = new AkTimer();
    $timer->RunEvery();
};

Worker::$logFile = LOG_FILE_PATH;
// 如果不是在根目录启动，则运行runAll方法
if (!defined('GLOBAL_START')) {
    Worker::runAll();
}
