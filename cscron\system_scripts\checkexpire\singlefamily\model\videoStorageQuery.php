<?php
require_once(dirname(__FILE__) . '/../../data_confusion.php');

class SingleFamilyVideoStorageQuery 
{
    private $db;
    private $medooDb;

    const VIDEO_STORE_PROJECT_PERSONAL  = 1;
    const VIDEO_STORE_PROJECT_RESIDENCE = 2;
    const VIDEO_STORE_PROJECT_OFFICE    = 3;

    public function __construct($db, $medooDb) {
        $this->db = $db;
        $this->medooDb = $medooDb;
    }

    // 查找视频存储过期数据
    public function getExpireData($daysBefore)
    {
        $sth = $this->db->prepare("select B.UUID as DisUUID, B.Account as DisAccount, A.UUID as InsUUID, A.Account as InsAccount,
                            P.Account as PersonalAccount, V.PersonalAccountUUID, P.Name,F.<PERSON>ber,F.<PERSON>, P.<PERSON>,
                            P.<PERSON>, S.DistributorUUID as SubDisUUID, V.ExpireTime
                            FROM VideoStorage V 
                            INNER JOIN Account A ON A.UUID = V.InstallerUUID 
                            INNER JOIN Account B ON B.UUID = A.ParentUUID 
                            INNER JOIN PersonalAccount P ON P.UUID = V.PersonalAccountUUID 
                            INNER JOIN PersonalAccountUserInfo F ON F.UUID = P.UserInfoUUID 
                            LEFT  JOIN SubDisMngList S ON S.InstallerUUID = V.InstallerUUID 
                            WHERE V.IsEnable = 1 
                            AND (V.ExpireTime >= CURDATE() + INTERVAL (:daysBefore) DAY) 
                            AND (V.ExpireTime < CURDATE() + INTERVAL (:daysBefore + 1) DAY) 
                            AND  V.ProjectType =:projectType 
                            ORDER BY B.UUID, A.UUID");

        $sth->bindValue(':projectType', self::VIDEO_STORE_PROJECT_PERSONAL, PDO::PARAM_INT);
        $sth->bindParam(':daysBefore', $daysBefore, PDO::PARAM_INT);
        $sth->execute();
        $expireList = $sth->fetchALL(PDO::FETCH_ASSOC);

        // 解密字段
        foreach ($expireList as $row => $item) {
            $expireList[$row]['Name'] = DataConfusion::getInstance()->decrypt($item['Name']);
            $expireList[$row]['Email'] = DataConfusion::getInstance()->decrypt($item['Email']);
            $expireList[$row]['MobileNumber'] = DataConfusion::getInstance()->decrypt($item['MobileNumber']);
        }
        return $expireList;
    } 

    // 是否开启视频存储自动扣费
    public function IsUserEnableVideoStorageAutoPay($uuid, $autoPayType)
    {
        $sth = $this->db->prepare("select A.Status from SubscriptionList A left join SubscriptionEndUserList B on A.UUID = B.SubscriptionUUID
                            where B.SiteUUID = :uuid and B.Type = :autoPayType order by A.ID desc limit 1");
        $sth->bindParam(':uuid', $uuid, PDO::PARAM_STR);
        $sth->bindParam(':autoPayType', $autoPayType, PDO::PARAM_INT);
        $sth->execute();
        $result = $sth->fetch(PDO::FETCH_ASSOC);
        return $result && $result['Status'] == 1;
    }
}