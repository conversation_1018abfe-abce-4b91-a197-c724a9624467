<?php

require_once(dirname(__FILE__) . '/expireDataCheckUtil.php');
require_once(dirname(__FILE__) . '/../model/videoStorageQuery.php');

class CommunityVideoStorageControl extends CommunityExpireDataCheckUtil
{
    public $commonQuery;
    private $videoStorageQuery;

    public function __construct($db, $medooDb, $commonQuery)
    {
        parent::__construct($db, $medooDb);
        $this->commonQuery = $commonQuery;
        $this->videoStorageQuery = new CommunityVideoStorageQuery($db, $medooDb);
    }

    public function getExpireData($daysBefore)
    {
        // 获取过期数据
        $expireData = $this->videoStorageQuery->getExpireData($daysBefore);

        // 过滤掉迁移dis的社区
        $expireData = $this->filterMigrateDistributor($expireData);

        // 过滤掉自动扣费社区
        $expireData = $this->filterAutoPayCommunity($expireData);   

        return $expireData;
    }

    private function filterAutoPayCommunity($expireData)
    {
        $filteredData = [];
        foreach ($expireData as $data) {
            // apt 自动扣费
            if (isset($data['PersonalAccountUUID']) && strlen($data['PersonalAccountUUID']) > 0) {
                if (!$this->videoStorageQuery->IsEnableVideoStorageAutoPay($data['PersonalAccountUUID'], AUTO_PAY_ENDUSER_ORDER_TYPE_COMMUNITY_ENDUSER_VIDEORECORD)
                && !$this->commonQuery->IsCommunityEndUserEnableMixAutoPay($data['PersonalAccountUUID'], AUTO_PAY_ORDER_TYPE_COMMUNITY_ENDUSER_VIDEORECORD)) {
                    $filteredData[] = $data;
                }
            } else { // 社区自动扣费
                if (!$this->videoStorageQuery->IsEnableVideoStorageAutoPay($data['CommunityUUID'], AUTO_PAY_ORDER_TYPE_COMMUNITY_VIDEO_STORAGE)
                && !$this->commonQuery->IsCommunityEnableMixAutoPay($data['CommunityUUID'], AUTO_PAY_ORDER_TYPE_COMMUNITY_VIDEO_STORAGE)) {
                    $filteredData[] = $data;
                }
            }
        }
        return $filteredData;
    }
}