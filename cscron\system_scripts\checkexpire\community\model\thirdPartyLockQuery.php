<?php

class CommunityThirdPartyLockQuery   
{
    private $db;
    private $medooDb;

    const DORMAKABA_LOCK = 3;
    const ITEC_LOCK = 6;
    const THIRD_PARTY_LOCK_PROJECT_RESIDENCE = 2;

    public function __construct($db, $medooDb) {
        $this->db = $db;
        $this->medooDb = $medooDb;
    }

    private function getChargeableLockBrands()
    {
        return [self::DORMAKABA_LOCK, self::ITEC_LOCK];
    }

    public function getExpireData($daysBefore)
    {
        // 获取收费的锁品牌数组
        $lockBrands = $this->getChargeableLockBrands();

        // 将锁品牌数组转换为字符串形式（例如：'3,6'）
        $brandList = implode(',', $lockBrands);

        $sql = "SELECT AA.UUID AS DisUUID, AA.Account AS DisAccount, T.InstallerUUID  AS InsUUID,
                    A.UUID AS CommunityUUID, A.Location AS Community, A.ID as CommunityID, A.TimeZone,
                    A.Language,  T.PersonalAccountUUID  AS PersonalAccountUUID,
                    T.CommunityUnitUUID  AS CommunityUnitUUID, T.Brand AS LockBrand, T.LockUUID AS LockUUID,
                    S.DistributorUUID AS SubDistributorUUID, T.ProjectType, T.ExpireTime
                FROM ThirdLockRelateInfo T INNER JOIN Account A ON A.UUID = T.AccountUUID 
                LEFT JOIN Account AA ON AA.UUID = A.ParentUUID
                LEFT JOIN SubDisMngList S ON S.InstallerUUID = T.InstallerUUID  
                WHERE T.Active = 1 
                AND T.ExpireTime > CURDATE() + INTERVAL :daysBefore DAY
                AND T.ExpireTime <= CURDATE() + INTERVAL (:daysBefore + 1) DAY
                AND T.ProjectType = :projectType AND T.Brand IN ($brandList) AND T.LockUUID IS NOT NULL
                ORDER BY A.ParentUUID, T.InstallerUUID, A.ID, T.PersonalAccountUUID";

        $sth = $this->db->prepare($sql);
        $sth->bindValue(':daysBefore', $daysBefore, PDO::PARAM_INT);
        $sth->bindValue(':projectType', self::THIRD_PARTY_LOCK_PROJECT_RESIDENCE, PDO::PARAM_INT);

        $sth->execute();
        $expireList = $sth->fetchAll(PDO::FETCH_ASSOC);
        return $expireList;
    }

    public function getDormakabaLockInfo($lockUUID)
    {
        $sth = $this->db->prepare("select Name as LockName, Grade from DormakabaLock where UUID = :uuid");
        $sth->bindParam(':uuid', $lockUUID, PDO::PARAM_STR);
        $sth->execute();
        return $sth->fetch(PDO::FETCH_ASSOC);
    }

    public function getItecLockInfo($lockUUID)
    {
        $sth = $this->db->prepare("select L.Name as LockName, L.Grade from ITecLock L inner join ITecGateway G on L.ITecGatewayUUID = G.UUID where L.UUID = :uuid;");
        $sth->bindParam(':uuid', $lockUUID, PDO::PARAM_STR);
        $sth->execute();
        return $sth->fetch(PDO::FETCH_ASSOC);
    }
}