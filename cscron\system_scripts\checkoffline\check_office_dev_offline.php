<?php
/* 办公设备离线通知 */
require_once(dirname(__FILE__) . '/../common/db_common.php');
require_once(dirname(__FILE__) . '/../common/email_common.php');
require_once(dirname(__FILE__) . '/../checkexpire/time.php');

const DEV_LIMIT = 1024;                             // 每次邮件的最大通知设备数
const DELAY_PROCESS_TIME = 600;                     // 离线延迟处理时间 600 seconds
const OFFICE_OFFLINE_NOTIFY = (1 << 1);             // CommunityInfo表中Switch字段用于控制离线通知的二进制位（二进制的第1位）
const DCLI_OFFLINE_CHECK_VERSION = 4600;            // 需要通知的Dclient版本
const OFFICE_DEV_OFFLINE = "office_dev_offline_notify";

// 查看 是否启用设备离线通知
const sql1 = "SELECT EnableDevOfflineNotify FROM SystemSetting;";

// 查找 有新的离线设备 且 离线时间10~20分钟 且 开启通知的项目
const sql2 = "SELECT A.ID AS AccountID,A.UUID,A.Location AS Community,A.TimeZone,A.Language,D.Location,D.MAC FROM Devices D 
        INNER JOIN Account A ON A.ID = D.MngAccountID 
        INNER JOIN OfficeInfo O ON O.AccountUUID = A.UUID 
        where D.Status=0 AND O.LastDevOfflineNotifyTime < D.LastDisConn 
        AND D.DclientVer>:dclinet_version AND ((O.Switch & :offline_notify) > 0)
        AND (unix_timestamp(D.LastDisConn) > unix_timestamp() - :delay_time * 3)
        AND (unix_timestamp(D.LastDisConn) < unix_timestamp() - :delay_time)
        ORDER BY A.ID;";

// 获取被通知的PM姓名、邮箱
const sql3 = "SELECT P.FirstName, P.LastName, AU.Email FROM PropertyInfo P 
        INNER JOIN PropertyMngList L ON P.AccountID = L.PropertyID 
        INNER JOIN Account A ON A.ID = L.PropertyID 
        INNER JOIN AccountMap AP ON AP.AccountUUID=A.UUID 
        INNER JOIN AccountUserInfo AU ON AU.UUID=AP.UserInfoUUID WHERE L.CommunityID=:account_id;";

// 更新最近一次的邮件通知的时间
const sql4 = "UPDATE OfficeInfo O INNER JOIN Account A ON A.UUID=O.AccountUUID 
              SET O.LastDevOfflineNotifyTime=CURRENT_TIMESTAMP WHERE A.ID=:account_id";

// 插入设备离线日志记录
const sql5 = "INSERT INTO DevOfflineLog(MngAccountID,Quantity,MacList) VALUE(:account_id, :quantity, :macs);";

function CheckOfficeDevOffline()
{
    if (!EnableDevOfflineNotify()) {
        LOG_INFO("SystemSetting.EnableDevOfflineNotify is not enabled, nothing to do.");
        return;
    }

    global $db;

    // 1.查找 有新的离线设备 且 离线时间10~20分钟 且 开启通知的项目
    $sth = $db->prepare(sql2);
    $sth->bindValue(':delay_time', DELAY_PROCESS_TIME, PDO::PARAM_INT);
    $sth->bindValue(':offline_notify', OFFICE_OFFLINE_NOTIFY, PDO::PARAM_INT);
    $sth->bindValue(':dclinet_version', DCLI_OFFLINE_CHECK_VERSION, PDO::PARAM_INT);
    $sth->execute();

    $time_str = "";
    $pre_mac_list = "";
    $pre_language = "";
    $pre_timezone = "";
    $pre_community = "";
    $pre_account_id = -1;
    $pre_device_count = 0;
    $pre_account_uuid = "";
    $pre_location_list = "";
    $device_lists = $sth->fetchALL(PDO::FETCH_ASSOC);
    array_push($device_lists, array("AccountID" => -1));  /// 末尾插入空行，便于处理最后一条数据

    $emailInfo = array();
    $emailInfo["email_type"] = OFFICE_DEV_OFFLINE;
    $emailInfo["project_type"] = PROJECT_TYPE_OFFICE;
    foreach ($device_lists as $row => $device) {
        // 上个项目结束，发送邮件后重置变量
        if ($pre_account_id != $device["AccountID"]) {
            if ($pre_device_count > 0) {
                $emailInfo["quantity"] = strval($pre_device_count);
                $emailInfo["location_list"] = $pre_location_list;
                $emailInfo["project_uuid"] = $pre_account_uuid;
                $emailInfo["community"] = $pre_community;
                $emailInfo["language"] = $pre_language;
                $emailInfo["mac_list"] = $pre_mac_list;
                $emailInfo["time"] = $time_str;

                sendOfficeDevOfflineEmail($pre_account_id, $emailInfo);
                UpdateLastDevOfflineNotifyTime($pre_account_id);
                AddDevOfflineLog($pre_account_id, $pre_device_count, $pre_mac_list);
            }

            // 重置变量
            $pre_mac_list = "";
            $pre_device_count = 0;
            $pre_location_list = "";
            $pre_account_id = $device["AccountID"];
            $pre_account_uuid = isset($device["UUID"]) ? $device["UUID"] : "";
            $pre_language = isset($device["Language"]) ? $device["Language"] : "";
            $pre_community = isset($device["Community"]) ? $device["Community"] : "";
            $pre_timezone = isset($device["TimeZone"]) ? $device["TimeZone"] : "+0:00 Troll";
            // 获取当前时区（含夏令时）下的当前时间字符串
            $time_str = setTimeZone(date('Y-m-d H:i:s'), $pre_timezone, 3);   
        }

        // 添加当前设备
        if (isset($device["MAC"]) && isset($device["Location"])) {
            $pre_device_count += 1;
            $pre_mac_list .= $device["MAC"] . ";";
            $pre_location_list .= $device["Location"] . ";";
        }
    }
}

function EnableDevOfflineNotify()
{
    global $db;

    $sth = $db->prepare(sql1);
    $sth->execute();
    $result = $sth->fetch(PDO::FETCH_ASSOC);
    if (array_key_exists("EnableDevOfflineNotify", $result) && $result["EnableDevOfflineNotify"] == 1) {
        return true;
    }

    return false;
}

function sendOfficeDevOfflineEmail($account_id, $emailInfo)
{
    global $db;

    // 获取被通知的PM姓名、邮箱
    $sth = $db->prepare(sql3);
    $sth->bindParam(':account_id', $account_id, PDO::PARAM_INT);
    $sth->execute();

    $pm_info_list = $sth->fetchALL(PDO::FETCH_ASSOC);
    foreach ($pm_info_list as $row => $pm_info) {
        $emailInfo["email"] = AES256Base64Decrypt($pm_info["Email"], EMAIL_AES_ENCRYPT_KEY, EMAIL_AES_ENCRYPT_IV);
        $emailInfo["name"] = $pm_info["FirstName"] . " " . $pm_info["LastName"];
        sendEmailNotify($emailInfo);
    }
}

function UpdateLastDevOfflineNotifyTime($account_id)
{
    global $db;

    // 更新最近一次的邮件通知的时间
    $sth = $db->prepare(sql4);
    $sth->bindParam(':account_id', $account_id, PDO::PARAM_INT);
    $sth->execute();
}

function AddDevOfflineLog($account_id, $quantity, $mac_list)
{
    global $db;

    // 更新最近一次的邮件通知的时间
    $sth = $db->prepare(sql5);
    $sth->bindParam(':account_id', $account_id, PDO::PARAM_INT);
    $sth->bindParam(':quantity', $quantity, PDO::PARAM_INT);
    $sth->bindParam(':macs', $mac_list, PDO::PARAM_STR);
    $sth->execute();
}

$db = getDB();
CheckOfficeDevOffline();
