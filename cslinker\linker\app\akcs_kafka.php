<?php

ini_set('date.timezone', 'Asia/Shanghai');
require_once(__DIR__.'/common/define.php');
require_once __DIR__ . '/akcs_monitor.php';
require_once __DIR__ . '/akcs_log.php';
require_once __DIR__ . '/akcs_traceid.php';

class AkKafkaProducer
{
    public static $topicName = 'ak_csroute';
    
    public static function sendMsg($data, $topicName = '') {
        $producer = self::createProducer();
        $topicName = empty($topicName) ? self::$topicName : $topicName;
        $topic = $producer->newTopic($topicName);
        // LOG_INFO(date('Y-m-d H:i:s').',ready to send to kafka, topic:' . $topicName . ',data:' . print_r($data,true));
        //RD_KAFKA_PARTITION_UA 不指定分区 自动分配
        $topic->produce(RD_KAFKA_PARTITION_UA, 0, $data);
        //非阻塞调用
        $producer->poll(0);
        //设置超时10s，如果未成功，消息可能投递失败，也可以设置为-1，无限等待。
        $result = $producer->flush(10000);
        if (RD_KAFKA_RESP_ERR_NO_ERROR !== $result) {
            LOG_INFO("AkKafkaProducer Was unable to flush, messages might be lost! data:".print_r($data, true) . ', topic:' . $topicName);
            return false;
        }
        return true;
    }

    private static function createProducer() {
        $conf = new \RdKafka\Conf();
        $conf->set('metadata.broker.list', KAFKA_INNER_IP);
        $producer = new RdKafka\Producer($conf);
        return $producer;
    }
}

class AkKafkaConsumer
{
    private $consumer;
    private $kafka_conf;
    private $topic;
    private $consumer_group;

    public function __construct($topic, $consumer_group)
    {
        $this->topic = $topic;
        $this->consumer_group = $consumer_group;

        //https://arnaud.le-blanc.net/php-rdkafka-doc/phpdoc/book.rdkafka.html  接口手册
        $this->kafka_conf = new RdKafka\Conf();
        // Set a rebalance callback to log partition assignments (optional)
        // 当有新的消费进程加入或者退出消费组时，kafka 会自动重新分配分区给消费者进程，这里注册了一个回调函数，当分区被重新分配时触发
        $this->kafka_conf->setRebalanceCb(function (RdKafka\KafkaConsumer $kafka, $err, array $partitions = null) {
            switch ($err) {
                case RD_KAFKA_RESP_ERR__ASSIGN_PARTITIONS:
                    echo "Assign: ";
                    var_dump($partitions);
                    $kafka->assign($partitions);
                    break;

                case RD_KAFKA_RESP_ERR__REVOKE_PARTITIONS:
                    echo "Revoke: ";
                    var_dump($partitions);
                    $kafka->assign(null);
                    break;

                default:
                    throw new \Exception($err);
            }
        });

        // 配置groud.id 具有相同 group.id 的consumer 将会处理不同分区的消息，
        //所以同一个组内的消费者数量如果订阅了一个topic， 那么消费者进程的数量多于这个topic 分区的数量是没有意义的。
        $this->kafka_conf->set('group.id', $this->consumer_group);
        $this->kafka_conf->set('enable.auto.commit', 'false');
        $this->kafka_conf->set('max.poll.interval.ms', 3600000);
        //添加 kafka集群服务器地址
        $this->kafka_conf->set('metadata.broker.list', KAFKA_INNER_IP);
        $topicConf = new RdKafka\TopicConf();
        //当没有初始偏移量时，从哪里开始读取
        $topicConf->set('auto.offset.reset', 'earliest');
        // Set the configuration to use for subscribed/assigned topics
        #$this->kafka_conf->setDefaultTopicConf($topicConf);
        $this->consumer = new RdKafka\KafkaConsumer($this->kafka_conf);
        // 让消费者订阅log 主题
        $this->consumer->subscribe([$this->topic]);
    }

    public function Start()
    {
        while (true) {
            $message = $this->consumer->consume(120*1000);
            switch ($message->err) {
                case RD_KAFKA_RESP_ERR_NO_ERROR:
                    // 解析消息并设置 TraceId
                    $messageData = json_decode($message->payload, true);
                    if ($messageData && isset($messageData['trace_id'])) 
                    {
                        TraceId::getInstance()->set($messageData['trace_id']);
                    }
                    
                    // 与服务端建立socket连接
                    $client = stream_socket_client('tcp://127.0.0.1:8000');
                    // 以text协议发送buffer1数据
                    fwrite($client, $message->payload);
                    LOG_INFO("get csroute kafka message:" . $message->payload);

                    $this->consumer->commit();
                    break;
                case RD_KAFKA_RESP_ERR__PARTITION_EOF:
                    // echo "No more messages; will wait for more ".$this->consumer_group."\n";
                    break;
                case RD_KAFKA_RESP_ERR__TIMED_OUT:
                    //echo "Timed out ".$this->consumer_group."\n";
                    break;
                default:
                    AddMonitorCslinkerConnectKafkaAlarm("cslinker connect to kafka error");
                    throw new \Exception($message->errstr(), $message->err);
                    break;
            }
        }
    }
}