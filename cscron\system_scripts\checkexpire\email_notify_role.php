<?php

class EmailNotifyRole
{
    const SEND_TO_DIS = 1;    
    const SEND_TO_SUBDIS = 2;      
    const SEND_TO_INS = 3;      
    const SEND_TO_PM = 4;       
    const SEND_TO_ENDUSER = 5;  
    
    // 常量值到描述的映射
    private static $roleDescriptions = [
        self::SEND_TO_DIS => 'Distributor',
        self::SEND_TO_SUBDIS => 'Sub-Distributor',
        self::SEND_TO_INS => 'Installer',
        self::SEND_TO_PM => 'Property Manager',
        self::SEND_TO_ENDUSER => 'End User'
    ];
    
    /**
     * 获取角色的描述文本
     * @param int $roleId 角色ID
     * @return string 角色描述，如果不存在则返回'Unknown Role'
     */
    public static function getRoleDescription($roleId)
    {
        return self::$roleDescriptions[$roleId] ?? 'Unknown Role';
    }
    
    /**
     * 获取所有角色及其描述的映射
     * @return array 角色映射数组
     */
    public static function getAllRoleMappings()
    {
        return self::$roleDescriptions;
    }
}