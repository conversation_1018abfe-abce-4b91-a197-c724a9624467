#!/bin/bash

# 获取第一个参数作为时间检查标志
IS_TIME_CHECK=$1

if [ "$IS_TIME_CHECK" = "1" ]; then
    echo "执行时间检查"
	SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/" && pwd)"
	source "$SCRIPT_DIR/time_check.sh"
    # 如果在维护时间段内，直接退出
    if ! CheckTimeRange 15 16; then
        exit 1
    fi
    exit 0
fi


#凌晨15分 app过期检测
sed -i '/check_app_expire.php/d' /var/spool/cron/crontabs/root
if [ -z "`grep "/usr/local/akcs/cscron/checkexpire/check_app_expire.php" /var/spool/cron/crontabs/root`" ];then
	echo "5 15 * * * /usr/local/bin/php /usr/local/akcs/cscron/checkexpire/check_app_expire.php >>/var/log/cscronlog/cscronlog_err.log 2>&1 " >> /var/spool/cron/crontabs/root
fi

#办公账号过期检测
sed -i '/check_office_expire.php/d' /var/spool/cron/crontabs/root
if [ -z "`grep "/usr/local/akcs/cscron/checkexpire/check_office_expire.php" /var/spool/cron/crontabs/root`" ];then
	echo "10 15 * * * /usr/local/bin/php /usr/local/akcs/cscron/checkexpire/check_office_expire.php >>/var/log/cscronlog/cscronlog_err.log 2>&1 " >> /var/spool/cron/crontabs/root
fi

#yale锁token刷新检测
sed -i '/check_yale_token_expire.php/d' /var/spool/cron/crontabs/root
if [ -z "`grep "/usr/local/akcs/cscron/checkexpire/check_yale_token_expire.php" /var/spool/cron/crontabs/root`" ];then
	echo "15 15 * * * /usr/local/bin/php /usr/local/akcs/cscron/checkexpire/check_yale_token_expire.php >>/var/log/cscronlog/cscronlog_err.log 2>&1 " >> /var/spool/cron/crontabs/root
fi

#qrio锁token刷新脚本，每周一凌晨2点刷新
sed -i '/check_qrio_id_token_expire.php/d' /var/spool/cron/crontabs/root
if [ -z "`grep "/usr/local/akcs/cscron/checkexpire/check_qrio_id_token_expire.php" /var/spool/cron/crontabs/root`" ];then
	echo "20 15 * * 1 /usr/local/bin/php /usr/local/akcs/cscron/checkexpire/check_qrio_id_token_expire.php >>/var/log/cscronlog/cscronlog_err.log 2>&1 " >> /var/spool/cron/crontabs/root
fi

#清除capture bash->php
sed -i '/clearCapture2.php/d' /var/spool/cron/crontabs/root
if [ -z "`grep "/usr/local/akcs/cscron/clearCapture2.php" /var/spool/cron/crontabs/root`" ];then
	echo "30 15 * * * /usr/local/bin/php /usr/local/akcs/cscron/clearCapture2.php >>/var/log/cscronlog/cscronlog_err.log 2>&1 " >> /var/spool/cron/crontabs/root
fi

#清除Model bash->php
sed -i '/clearModel.php/d' /var/spool/cron/crontabs/root
if [ -z "`grep "/usr/local/akcs/cscron/clearModel.php" /var/spool/cron/crontabs/root`" ];then
	echo "30 15 * * * /usr/local/bin/php /usr/local/akcs/cscron/clearModel.php >>/var/log/cscronlog/cscronlog_err.log 2>&1 " >> /var/spool/cron/crontabs/root
fi

#每天15点35分，检查RentManager过期
sed -i '/check_rent_expire.php/d' /var/spool/cron/crontabs/root
if [ -z "`grep "/usr/local/akcs/cscron/checkexpire/rent_manager/check_rent_expire.php" /var/spool/cron/crontabs/root`" ];then
	echo "35 15 * * * /usr/local/bin/php /usr/local/akcs/cscron/checkexpire/rent_manager/check_rent_expire.php >>/var/log/cscronlog/cscronlog_err.log 2>&1 " >> /var/spool/cron/crontabs/root
fi

#每天15点40分，检查视频存储过期
sed -i '/check_video_record_expire.php/d' /var/spool/cron/crontabs/root
if [ -z "`grep "/usr/local/akcs/cscron/checkexpire/check_video_record_expire.php" /var/spool/cron/crontabs/root`" ];then
	echo "40 15 * * * /usr/local/bin/php /usr/local/akcs/cscron/checkexpire/check_video_record_expire.php >>/var/log/cscronlog/cscronlog_err.log 2>&1 " >> /var/spool/cron/crontabs/root
fi

#每天15点45分，检查视频存储过期(预留10分钟执行时间)
sed -i '/check_multi_expire.php/d' /var/spool/cron/crontabs/root
if [ -z "`grep "/usr/local/akcs/cscron/checkexpire/newoffice/check_multi_expire.php" /var/spool/cron/crontabs/root`" ];then
	echo "45 15 * * * /usr/local/bin/php /usr/local/akcs/cscron/checkexpire/newoffice/check_multi_expire.php >>/var/log/cscronlog/cscronlog_err.log 2>&1 " >> /var/spool/cron/crontabs/root
fi

#每隔10分钟 检查设备离线推送（社区）
sed -i '/check_community_dev_offline.php/d' /var/spool/cron/crontabs/root
if [ -z "`grep "/usr/local/akcs/cscron/checkoffline/check_community_dev_offline.php" /var/spool/cron/crontabs/root`" ];then
	echo "*/10 * * * * /usr/local/bin/php /usr/local/akcs/cscron/checkoffline/check_community_dev_offline.php >>/var/log/cscronlog/cscronlog_err.log 2>&1 " >> /var/spool/cron/crontabs/root
fi

#每隔10分钟 检查设备离线推送（办公）
sed -i '/check_office_dev_offline.php/d' /var/spool/cron/crontabs/root
if [ -z "`grep "/usr/local/akcs/cscron/checkoffline/check_office_dev_offline.php" /var/spool/cron/crontabs/root`" ];then
	echo "*/10 * * * * /usr/local/bin/php /usr/local/akcs/cscron/checkoffline/check_office_dev_offline.php >>/var/log/cscronlog/cscronlog_err.log 2>&1 " >> /var/spool/cron/crontabs/root
fi

#每天15点45分，检查三方锁过期
sed -i '/check_third_party_lock_expire.php/d' /var/spool/cron/crontabs/root
if [ -z "`grep "/usr/local/akcs/cscron/checkexpire/check_third_party_lock_expire.php" /var/spool/cron/crontabs/root`" ];then
	echo "45 15 * * * /usr/local/bin/php /usr/local/akcs/cscron/checkexpire/check_third_party_lock_expire.php >>/var/log/cscronlog/cscronlog_err.log 2>&1 " >> /var/spool/cron/crontabs/root
fi